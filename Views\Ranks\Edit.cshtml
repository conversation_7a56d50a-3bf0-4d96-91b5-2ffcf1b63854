@model TajneedApp.Models.Rank

@{
    ViewData["Title"] = "Edit";
}

<h1>Edit</h1>

<h4>Rank</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="RankId" />
            <div class="form-group">
                <label asp-for="RankName" class="control-label"></label>
                <input asp-for="RankName" class="form-control" />
                <span asp-validation-for="RankName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
