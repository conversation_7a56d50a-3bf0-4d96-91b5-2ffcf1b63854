@model TajneedApp.Models.Category

@{
    ViewData["Title"] = "إضافة فئة جديدة";
}

<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h3 class="m-0 font-weight-bold">إضافة فئة جديدة</h3>
        </div>
        <div class="card-body">
            <form asp-action="Create">
                <div class="form-group">
                    <label asp-for="CategoryName" class="control-label"></label>
                    <input asp-for="CategoryName" class="form-control" />
                    <span asp-validation-for="CategoryName" class="text-danger"></span>
                </div> 
                <div class="form-group">
                    <label asp-for="Description" class="control-label"></label>
                    <input asp-for="Description" class="form-control" />
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>

                <div class="mb-2">

                </div>
                <div class="form-group ">
                    <input type="submit" value="إضافة" class="btn btn-primary" />
                </div>
            </form>
            <a asp-action="Index" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة إلى القائمة
            </a>
        </div>
    </div>
</div>