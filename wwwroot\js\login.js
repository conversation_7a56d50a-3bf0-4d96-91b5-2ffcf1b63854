// Login page enhancements
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.login-form');
    const inputs = document.querySelectorAll('.modern-input');
    const submitBtn = document.querySelector('.btn-login');
    const submitBtnText = document.querySelector('.btn-text');
    
    // Add input validation and styling
    inputs.forEach(input => {
        // Add focus/blur effects
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            validateInput(this);
        });
        
        // Real-time validation
        input.addEventListener('input', function() {
            clearTimeout(this.validationTimeout);
            this.validationTimeout = setTimeout(() => {
                validateInput(this);
            }, 500);
        });
    });
    
    // Form submission handling
    if (form) {
        form.addEventListener('submit', function(e) {
            // Add loading state
            submitBtn.classList.add('loading');
            submitBtnText.textContent = 'جاري تسجيل الدخول...';
            
            // Validate all inputs before submission
            let isValid = true;
            inputs.forEach(input => {
                if (!validateInput(input)) {
                    isValid = false;
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                submitBtn.classList.remove('loading');
                submitBtnText.textContent = 'تسجيل الدخول';
                return false;
            }
        });
    }
    
    // Input validation function
    function validateInput(input) {
        const value = input.value.trim();
        const fieldName = input.name;
        let isValid = true;
        let errorMessage = '';
        
        // Remove existing validation classes
        input.classList.remove('success', 'error');
        
        // Basic validation
        if (!value) {
            isValid = false;
            errorMessage = getRequiredMessage(fieldName);
        } else {
            // Field-specific validation
            switch (fieldName) {
                case 'ServiceNumber':
                    if (!/^\d+$/.test(value)) {
                        isValid = false;
                        errorMessage = 'الرقم العسكري يجب أن يحتوي على أرقام فقط';
                    } else if (value.length < 3) {
                        isValid = false;
                        errorMessage = 'الرقم العسكري قصير جداً';
                    }
                    break;
                    
                case 'Password':
                    if (value.length < 6) {
                        isValid = false;
                        errorMessage = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                    }
                    break;
            }
        }
        
        // Apply validation styling
        if (isValid) {
            input.classList.add('success');
            clearFieldError(input);
        } else {
            input.classList.add('error');
            showFieldError(input, errorMessage);
        }
        
        return isValid;
    }
    
    // Show field error
    function showFieldError(input, message) {
        const errorSpan = input.parentElement.parentElement.querySelector('.field-validation-error');
        if (errorSpan) {
            errorSpan.textContent = message;
            errorSpan.style.display = 'block';
        }
    }
    
    // Clear field error
    function clearFieldError(input) {
        const errorSpan = input.parentElement.parentElement.querySelector('.field-validation-error');
        if (errorSpan) {
            errorSpan.textContent = '';
            errorSpan.style.display = 'none';
        }
    }
    
    // Get required field message
    function getRequiredMessage(fieldName) {
        const messages = {
            'ServiceNumber': 'الرقم العسكري مطلوب',
            'Password': 'كلمة المرور مطلوبة'
        };
        return messages[fieldName] || 'هذا الحقل مطلوب';
    }
    
    // Add floating label effect
    inputs.forEach(input => {
        const wrapper = input.parentElement;
        const label = wrapper.parentElement.querySelector('.form-label');
        
        function updateLabelState() {
            if (input.value || input === document.activeElement) {
                label.classList.add('floating');
            } else {
                label.classList.remove('floating');
            }
        }
        
        input.addEventListener('focus', updateLabelState);
        input.addEventListener('blur', updateLabelState);
        input.addEventListener('input', updateLabelState);
        
        // Initial state
        updateLabelState();
    });
    
    // Add ripple effect to button
    submitBtn.addEventListener('click', function(e) {
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');
        
        this.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    });
    
    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.target.classList.contains('modern-input')) {
            const inputs = Array.from(document.querySelectorAll('.modern-input'));
            const currentIndex = inputs.indexOf(e.target);
            
            if (currentIndex < inputs.length - 1) {
                e.preventDefault();
                inputs[currentIndex + 1].focus();
            } else {
                submitBtn.click();
            }
        }
    });
});
