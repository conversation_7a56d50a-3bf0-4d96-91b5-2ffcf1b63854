﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
namespace TajneedApp.Models;

public partial class Category
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]    
    public int CategoryId { get; set; }
    [Display(Name = "الفئة")]
    [Required(ErrorMessage = "اسم الفئة مطلوب")]
    public string? CategoryName { get; set; }
    [Display(Name = "الوصف")]
    [Required(ErrorMessage = " الوصف مطلوب")]  
    public string? Description { get; set; }
    [Display(Name = "رمز الفئة")]
    [Required(ErrorMessage = "  رمز الفئة مطلوب")] 
    public string? CategoryCode { get; set; }

    [ValidateNever]
    public virtual ICollection<Candidate> Candidates { get; set; } = new List<Candidate>();
    [ValidateNever]   
   public virtual ICollection<Exam> Exams { get; set; } = new List<Exam>();
}
