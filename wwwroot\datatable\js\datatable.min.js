"use strict";var DataTable=function(t,e){for(var i in this.options={},this.table=t,this.currentPage=0,this.currentStart=0,this.filterIndex=[],DataTable.defaultOptions)DataTable.defaultOptions.hasOwnProperty(i)&&(e.hasOwnProperty(i)?this.options[i]=e[i]:this.options[i]=DataTable.defaultOptions[i]);e.hasOwnProperty("data")&&(this.options.data=e.data),this.options.nbColumns<0&&(this.options.nbColumns=this.table.tHead.rows[0].cells.length),this.pagingDivs=document.querySelectorAll(this.options.pagingDivSelector);for(var s=0;s<this.pagingDivs.length;++s){var a=this.pagingDivs[s];this.addClass(a,"pagination-datatables"),this.addClass(a,this.options.pagingDivClass);var n=document.createElement("ul");this.addClass(n,this.options.pagingListClass),a.appendChild(n)}this.pagingLists=document.querySelectorAll(this.options.pagingDivSelector+" ul"),this.counterDivs=document.querySelectorAll(this.options.counterDivSelector),this.loadingDiv=document.querySelector(this.options.loadingDivSelector);this.table.tHead||(this.table.tHead=document.createElement("thead"),this.table.appendChild(this.table.rows[0])),this.options.forceStrings&&(this.options.dataTypes=!1);this.table.tHead.rows[0].cells;if(this.table.tBodies[0]||(this.table.tBodies[0]=document.createElement("tbody")),this.options.data instanceof Array)this.data=this.options.data;else if(this.options.data instanceof Object){var r=DataTable.defaultAjaxOptions;for(var i in this.options.data)r[i]=this.options.data[i];this.options.data=r;if(void 0!==this.table.dataset.size&&(this.options.data.size=parseInt(this.table.dataset.size,10)),this.data=[],void 0!==this.options.data.size)if(this.loadingDiv.innerHTML='<div class="progress datatable-load-bar"><div class="progress-bar progress-bar-striped active" style="width: 0%;"></div></div>',this.options.data.allInOne)this.getAjaxDataAsync(!0);else for(s=0;s<this.options.data.size;s+=this.options.pageSize*this.options.pagingNumberOfPages)this.getAjaxDataAsync(s);else this.loadingDiv.innerHTML='<div class="progress datatable-load-bar"><div class="progress-bar progress-bar-striped active" style="width: 0%;"></div></div>',this.getAjaxDataAsync(0,!0)}else if(this.table.tBodies[0].rows.length>0){this.data=[];var o=this.table.tBodies[0].rows,l=o[0].cells.length;for(s=0;s<o.length;++s)this.data.push([]);for(var h=0;h<l;++h){var d=function(t){return t};if(this.options.dataTypes instanceof Array)switch(this.options.dataTypes[h]){case"int":d=parseInt;break;case"float":case"double":d=parseFloat;break;case"date":case"datetime":d=function(t){return new Date(t)};break;case!1:case!0:case"string":case"str":d=function(t){return t};break;default:d=this.options.dataTypes[h]}for(s=0;s<o.length;++s)this.data[s].push(d(o[s].cells[h].innerHTML.trim()))}if(!0===this.options.dataTypes)for(var c=0;c<this.data[0].length;++c){var p=!0;for(s=0;s<this.data.length;++s)""===this.data[s][c]||this.data[s][c]-parseFloat(this.data[s][c])+1>=0||(p=!1);if(p)for(s=0;s<this.data.length;++s)""!==this.data[s][c]&&(this.data[s][c]=parseFloat(this.data[s][c]))}}this.createSort(),this.createFilter(),this.triggerSort(),this.filter()};DataTable.prototype={constructor:DataTable,addClass:function(t,e){"string"==typeof e&&(e=e.split(" ")),e.forEach(function(e){e&&t.classList.add(e)})},removeNode:function(t){t&&t.parentNode.removeChild(t)},setRefreshTimeout:function(){var t;this.options.data.refresh&&(clearTimeout(this.refreshTimeOut),this.refreshTimeOut=setTimeout((t=this,function(){t.getAjaxDataAsync(0,!0)}),this.options.data.refresh))},hideLoadingDivs:function(){this.removeNode(this.loadingDiv)},updateLoadingDivs:function(){this.data.length>=this.options.data.size?(this.setRefreshTimeout(),this.hideLoadingDivs()):this.loadingDiv.querySelector("div.progress .progress-bar").style.width=parseInt(100*this.data.length/this.options.data.size,10)+"%"},getAjaxDataAsync:function(t,e){void 0===e&&(e=!1),e&&void 0===this.syncData&&(this.syncData={data:[],toAdd:[],toUpdate:{},toDelete:[]});var i,s,a,n=new XMLHttpRequest;n.timeout=this.options.data.timeout,n.onreadystatechange=(i=this,s=t,a=e,function(){if(4==this.readyState)switch(this.status){case 200:if(a)if(this.response.length>0)i.syncData.data=i.syncData.data.concat(this.response),i.getAjaxDataAsync(s+i.options.pageSize*i.options.pagingNumberOfPages,!0);else{var t=i.syncData;for(var e in delete i.syncData,i.data=t.data,i.addRows(t.toAdd),t.toDelete.forEach(function(t){t instanceof Function?i.deleteAll(t):i.deleteRow(t)}),t.toUpdate)i.updateRow(e,t.toUpdate[e]);i.sort(!0),i.setRefreshTimeout()}else i.data=i.data.concat(this.response),i.updateLoadingDivs(),i.sort(!0);break;case 404:case 500:console.log("ERROR: "+this.status+" - "+this.statusText),console.log(n);break;default:i.getAjaxDataAsync(s,a)}});var r=this.options.data.url,o=this.options.pageSize*this.options.pagingNumberOfPages,l=new FormData;!0!==t&&("GET"==this.options.data.type.toUpperCase()?r+="?start="+t+"&limit="+o:(l.append("offset",t),l.append("limit",o))),n.open(this.options.data.type,r,!0),n.responseType="json",n.send(l)},getLastPageNumber:function(){return parseInt(Math.ceil(this.filterIndex.length/this.options.pageSize),10)},createPagingLink:function(t,e,i){var s=document.createElement("a");return this.options.pagingLinkClass&&s.classList.add(this.options.pagingLinkClass),this.options.pagingLinkHref&&(s.href=this.options.pagingLinkHref),!1!==this.options.pagingLinkDisabledTabIndex&&i&&(s.tabIndex=this.options.pagingLinkDisabledTabIndex),s.dataset.page=e,s.innerHTML=t,s},updatePaging:function(){var t,e,i=this.options.pagingNumberOfPages,s=this,a=parseInt(this.currentStart/this.options.pageSize,10)+1,n=this.getLastPageNumber(),r=this.filterIndex.length?this.currentStart+1:0,o=this.currentStart+this.options.pageSize>this.filterIndex.length?this.filterIndex.length:this.currentStart+this.options.pageSize;a<i/2?t=1:a>=n-i/2?(t=n-i+1)<1&&(t=1):t=parseInt(a-i/2+1,10),e=t+i<n+1?t+i-1:n;for(var l=0;l<this.pagingLists.length;++l){var h=[];if(s.options.firstPage)(p=document.createElement("li")).appendChild(s.createPagingLink(s.options.firstPage,"first",1===a)),1===a&&p.classList.add("active"),h.push(p);if(s.options.prevPage)(p=document.createElement("li")).appendChild(s.createPagingLink(s.options.prevPage,"prev",1===a)),1===a&&p.classList.add("active"),h.push(p);if(s.options.pagingPages){var d=this.options.pagingPages.call(this.table,t,e,a,r,o);d instanceof Array?h=h.concat(d):h.push(d)}else for(var c=t;c<=e;c++){var p;(p=document.createElement("li")).appendChild(s.createPagingLink(c,c,a===c)),c===a&&p.classList.add("active"),h.push(p)}if(s.options.nextPage)(p=document.createElement("li")).appendChild(s.createPagingLink(s.options.nextPage,"next",a===n||0===n)),a!==n&&0!==n||p.classList.add("active"),h.push(p);if(s.options.lastPage)(p=document.createElement("li")).appendChild(s.createPagingLink(s.options.lastPage,"last",a===n||0===n)),a!==n&&0!==n||p.classList.add("active"),h.push(p);this.pagingLists[l].innerHTML="",h.forEach(function(t){s.options.pagingItemClass&&t.classList.add(s.options.pagingItemClass),t.childNodes.length>0&&t.childNodes[0].addEventListener("click",function(t){if(t.preventDefault(),!this.parentNode.classList.contains("active")&&void 0!==this.dataset.page)switch(this.dataset.page){case"first":s.loadPage(1);break;case"prev":s.loadPage(a-1);break;case"next":s.loadPage(a+1);break;case"last":s.loadPage(n);break;default:s.loadPage(parseInt(parseInt(this.dataset.page),10))}},!1),this.pagingLists[l].appendChild(t)},this)}},updateCounter:function(){for(var t=this.filterIndex.length?parseInt(this.currentStart/this.options.pageSize,10)+1:0,e=parseInt(Math.ceil(this.filterIndex.length/this.options.pageSize),10),i=this.filterIndex.length?this.currentStart+1:0,s=this.currentStart+this.options.pageSize>this.filterIndex.length?this.filterIndex.length:this.currentStart+this.options.pageSize,a=0;a<this.counterDivs.length;++a)this.counterDivs[a].innerHTML=this.options.counterText.call(this.table,t,e,i,s,this.filterIndex.length,this.data.length)},getSortFunction:function(){if(!1===this.options.sort)return!1;if(this.options.sort instanceof Function)return this.options.sort;if(0===this.data.length||!(this.options.sortKey in this.data[0]))return!1;var t,e=this.options.sortKey,i="asc"===this.options.sortDir;return this.options.sort[e]instanceof Function?(t=this.options.sort[e],function(s,a){var n=s[e],r=a[e];return i?t(n,r):-t(n,r)}):function(t,s){var a=t[e],n=s[e];return a>n?i?1:-1:a<n?i?-1:1:0}},destroyFilter:function(){this.removeNode(this.table.querySelector(".datatable-filter-line"))},changePlaceHolder:function(){for(var t=this.options.filterText?this.options.filterText:"",e=this.table.querySelectorAll('.datatable-filter-line input[type="text"]'),i=0;i<e.length;++i)e[i].placeholder=t},createTextFilter:function(t){var e=this.options.filters[t],i=e instanceof HTMLInputElement?e:document.createElement("input");i.type="text",this.options.filterText&&(i.placeholder=this.options.filterText),this.addClass(i,"datatable-filter datatable-input-text"),i.dataset.filter=t,this.filterVals[t]="";var s,a,n=(s=0,function(t,e){clearTimeout(s),s=setTimeout(t,e)});i.onkeyup=(a=this,function(){var t=this.value.toUpperCase(),e=this.dataset.filter;n(function(){a.filterVals[e]=t,a.filter()},300)}),i.onkeydown=i.onkeyup;var r="regexp"===e||i.dataset.regexp;return e instanceof Function?this.addFilter(t,e):r?this.addFilter(t,function(t,e){return new RegExp(e).test(String(t))}):this.addFilter(t,function(t,e){return-1!==String(t).toUpperCase().indexOf(e)}),this.addClass(i,this.options.filterInputClass),i},_isIn:function(t,e){for(var i=!1,s=0;s<e.length&&!i;++s)i=e[s]==t;return i},_index:function(t,e){if(null==e)return-1;for(var i=-1,s=0;s<e.length&&-1==i;++s)e[s]===t&&(i=s);return i},_keys:function(t){if(null!=t){var e=[];for(var i in t)t.hasOwnProperty(i)&&e.push(i);return e}},createSelectFilter:function(t){var e=this.options.filters[t],i={},s=[],a=!1,n=!0,r=this.options.filterEmptySelect,o=!1;if(e instanceof HTMLSelectElement?o=e:e instanceof Object&&"element"in e&&e.element&&(o=e.element),e instanceof HTMLSelectElement||"select"===e)i=this.getFilterOptions(t);else if(a="multiple"in e&&!0===e.multiple,n="empty"in e&&e.empty,r="empty"in e&&"string"==typeof e.empty?e.empty:this.options.filterEmptySelect,"values"in e){if(i="auto"===e.values?this.getFilterOptions(t):e.values,"default"in e)s=e.default;else if(a)for(var l in s=[],i)i[l]instanceof Object?s=s.concat(this._keys(i[l])):s.push(l);else s=[];s instanceof Array||(s=[s])}else i=e,s=a?this._keys(i):[];var h=o||document.createElement("select");(a&&(h.multiple=!0),e.default&&(h.dataset.default=e.default),this.addClass(h,"datatable-filter datatable-select"),h.dataset.filter=t,n)&&((u=document.createElement("option")).dataset.empty=!0,u.value="",u.innerHTML=r,h.appendChild(u));var d=[];for(var c in i)if(i[c]instanceof Object){var p=document.createElement("optgroup");for(var f in p.label=c,i[c]){if(i[c].hasOwnProperty(f))d.push(f),(u=document.createElement("option")).value=f,u.selected=this._isIn(f,s),u.innerHTML=i[c][f],p.appendChild(u)}h.appendChild(p)}else{var u;d.push(c),(u=document.createElement("option")).value=c,u.selected=this._isIn(c,s),u.innerHTML=i[c],h.appendChild(u)}var g,v,m,y,b,S,D=h.value;if(a){D=[];for(var L=0;L<h.options.length;++L)h.options[L].selected&&D.push(h.options[L].value)}return this.filterVals[t]=a?D:n&&!D?d:[D],h.onchange=(g=d,v=a,m=n,y=this,function(){var t=this.value;if(v){t=[];for(var e=0;e<this.options.length;++e)this.options[e].selected&&t.push(this.options[e].value)}var i=this.dataset.filter;y.filterVals[i]=v?t:m&&!t?g:[t],y.filter()}),e instanceof Object&&e.fn instanceof Function?(this.addFilter(t,e.fn),h.dataset.filterType="function"):(this.addFilter(t,(b=d,S=this,function(t,e){return!!e&&(e==b&&!t||S._isIn(t,e))})),h.dataset.filterType="default"),this.addClass(h,this.options.filterSelectClass),h},createFilter:function(){if(this.filters=[],this.filterTags=[],this.filterVals=[],"*"===this.options.filters){var t=this.table.tHead.rows[0].cells.length;for(this.options.filters=[];t--;)this.options.filters.push(!0)}if(this.options.filters){var e=document.createElement("tr");for(var i in e.classList.add("datatable-filter-line"),this.options.filters)if(this.options.filters.hasOwnProperty(i)){var s=document.createElement("td");if(!1!==this.options.filters[i]){var a=this.options.filters[i],n=!0===a||"regexp"===a||"input"===a||a instanceof Function||a instanceof HTMLInputElement?this.createTextFilter(i):this.createSelectFilter(i);this.filterTags[i]=n,document.body.contains(n)||(s.classList.add("datatable-filter-cell"),s.appendChild(n))}this.options.filters[i]instanceof Object&&this.options.filters[i].noColumn||e.appendChild(s)}e.querySelectorAll("td.datatable-filter-cell").length>0&&this.table.tHead.appendChild(e)}},filter:function(t){void 0===t&&(t=!1);var e=this.currentStart;this.currentStart=0,this.filterIndex=[];for(var i=0;i<this.data.length;i++)this.checkFilter(this.data[i])&&this.filterIndex.push(i);if(t){for(this.currentStart=e;this.currentStart>=this.filterIndex.length;)this.currentStart-=this.options.pageSize;this.currentStart<0&&(this.currentStart=0)}if(this.options.filterSelectOptions&&this.filterIndex.length>0){for(var s=[],a=0;a<this.data[0].length;++a)s.push({});for(i=0;i<this.filterIndex.length;++i){var n=this.data[this.filterIndex[i]];for(a=0;a<n.length;++a)s[a][n[a]]=!0}for(var r=0;r<s.length;++r){var o=this._keys(s[r]);if(this.filterTags[r]&&this.filterTags[r]instanceof HTMLSelectElement&&"default"==this.filterTags[r].dataset.filterType){var l=this.filterTags[r].childNodes;for(i=0;i<l.length;++i)l[i].dataset.empty||(l[i].style.display=this._isIn(l[i].value,o)?"block":"none")}}}this.refresh()},resetFilters:function(){var t=this;this.filterTags.forEach(function(e){var i=e.dataset.filter;if(e instanceof HTMLInputElement)e.value="",t.filterVals[i]="";else if(e.multiple){for(var s=[],a=0;a<e.childNodes.length;++a)e.childNodes[a].selected=!0,s.push(e.childNodes[a].value);t.filterVals[i]=s}else if(e.dataset.default&&e.querySelector('option[value="'+e.dataset.default+'"]').length>0){for(a=0;a<e.childNodes.length;++a)e.childNodes[a].selected=e.childNodes[a].value==e.dataset.default;t.filterVals[i]=[e.dataset.default]}else if(e.childNodes.length>0){e.childNodes[0].selected=!0;for(a=1;a<e.childNodes.length;++a)e.childNodes[a].selected=!1;if(e.childNodes[0].dataset.empty){for(s=[],a=1;a<e.childNodes.length;++a)s.push(e.childNodes[a].value);t.filterVals[i]=s}else t.filterVals[i]=[e.childNodes[0].value]}}),this.filter()},stripTags:function(t){var e=document.createElement("div");return e.innerHTML=t,e.textContent||e.innerText},checkFilter:function(t){var e=!0;for(var i in this.filters){var s="_"===i[0]?t:t[i];if("string"==typeof s&&(s=this.stripTags(s)),!this.filters[i](s,this.filterVals[i])){e=!1;break}}return e},addFilter:function(t,e){this.filters[t]=e},getFilterOptions:function(t){var e={},i=[];for(var s in this.data)""!==this.data[s][t]&&i.push(this.data[s][t]);for(var a in i.sort(),i)if(i.hasOwnProperty(a)){var n=this.stripTags(i[a]);e[n]=n}return e},destroySort:function(){$("thead th").removeClass("sorting sorting-asc sorting-desc").unbind("click.datatable").removeData("sort")},createSort:function(){var t=this;if(!(this.options.sort instanceof Function))for(var e=0,i=this.table.tHead.rows[0].cells,s=0;s<i.length;++s){if(i[s].dataset.sort)t.options.sort=!0;else if("*"===t.options.sort)i[s].dataset.sort=e;else{var a;t.options.sort instanceof Array?a=e:t.options.sort instanceof Object&&(a=t._keys(t.options.sort)[e]),void 0!==a&&t.options.sort[a]&&(i[s].dataset.sort=a)}void 0!==i[s].dataset.sort&&i[s].classList.add("sorting"),e++,i[s].addEventListener("click",function(){if(this.dataset.sort){if(this.classList.contains("sorting-asc"))t.options.sortDir="desc",this.classList.remove("sorting-asc"),this.classList.add("sorting-desc");else if(this.classList.contains("sorting-desc"))t.options.sortDir="asc",this.classList.remove("sorting-desc"),this.classList.add("sorting-asc");else{for(var e=this.parentNode.cells,i=0;i<e.length;i++)e[i].classList.remove("sorting-desc"),e[i].classList.remove("sorting-asc");t.options.sortDir="asc",t.options.sortKey=this.dataset.sort,this.classList.add("sorting-asc")}t.sort(),t.refresh()}},!1)}},triggerSort:function(){if(this.options.sort instanceof Function)this.sort(),this.refresh();else if(!1!==this.options.sortKey){for(var t,e=this.table.tHead.rows[0].cells,i=0;i<e.length;i++)e[i].classList.remove("sorting-desc"),e[i].classList.remove("sorting-asc"),e[i].dataset.sort===this.options.sortKey&&(t=e[i]);void 0!==t&&(t.classList.add("sorting-"+this.options.sortDir),this.sort(),this.refresh())}},sort:function(t){var e=this.getSortFunction();!1!==e&&this.data.sort(e),this.filter(t)},identify:function(t,e){return!1!==this.options.identify&&(this.options.identify instanceof Function?this.options.identify(t,e):e[this.options.identify]==t)},indexOf:function(t){for(var e=-1,i=0;i<this.data.length&&-1===e;i++)this.identify(t,this.data[i])&&(e=i);return e},row:function(t){return!0===this.options.identify?this.data[t]:this.data[this.indexOf(t)]},all:function(t){if(void 0===t||!0===t)return this.data;for(var e=[],i=0;i<this.data.length;++i)t(this.data[i])&&e.push(this.data[i]);return e},addRow:function(t){this.data.push(t),void 0!==this.syncData&&this.syncData.toAdd.push(t),this.sort(),this.filter(),this.currentStart=parseInt(this._index(this._index(t,this.data),this.filterIndex)/this.options.pageSize,10)*this.options.pageSize,this.refresh()},addRows:function(t){this.data=this.data.concat(t),void 0!==this.syncData&&(this.syncData.toAdd=this.syncData.toAdd.concat(t)),this.sort(),this.filter(),this.currentStart=parseInt(this._index(this._index(t,this.data),this.filterIndex)/this.options.pageSize,10)*this.options.pageSize,this.refresh()},deleteRow:function(t){var e=this.currentStart,i=this.indexOf(t);-1!==i?(this.data.splice(i,1),void 0!==this.syncData&&this.syncData.toDelete.push(t),this.filter(),e<this.filterIndex.length?this.currentStart=e:(this.currentStart=e-this.options.pageSize,this.currentStart<0&&(this.currentStart=0)),this.refresh()):console.log("No data found with id: "+t)},deleteAll:function(t){var e=this.currentStart,i=[];void 0!==this.syncData&&this.syncData.toDelete.push(t);for(var s=0;s<this.data.length;++s)t(this.data[s])||i.push(this.data[s]);this.data=i,this.filter(),e<this.filterIndex.length?this.currentStart=e:(this.currentStart=e-this.options.pageSize,this.currentStart<0&&(this.currentStart=0)),this.refresh()},updateRow:function(t,e){var i=this.indexOf(t);if(void 0!==this.syncData&&(this.syncData.toUpdate[t]=e),-1!==i){for(var s in t in e&&delete e[t],this.data[i])s in e&&(this.data[i][s]=e[s]);this.sort(),this.filter(),this.currentStart=parseInt(this._index(this.indexOf(t),this.filterIndex)/this.options.pageSize,10)*this.options.pageSize,this.refresh()}},loadPage:function(t){var e=this.currentStart/this.options.pageSize;t<1?t=1:t>this.getLastPageNumber()&&(t=this.getLastPageNumber()),this.currentStart=(t-1)*this.options.pageSize,this.refresh(),this.options.onChange.call(this.table,e+1,t)},getCurrentPage:function(){return this.currentStart/this.options.pageSize+1},refresh:function(){if(this.options.beforeRefresh.call(this.table),this.updatePaging(),this.updateCounter(),this.table.tBodies[0].innerHTML="",this.currentStart>=this.currentDataLength)this.table.tBodies[0].innerHTML='<tr><td colspan="'+this.options.nbColumns+'"><div class="progress progress-striped active"><div class="bar" style="width: 100%;"></div></div></div></tr>';else{for(var t=0;t<this.options.pageSize&&t+this.currentStart<this.filterIndex.length;t++){var e=this.filterIndex[this.currentStart+t],i=this.data[e];this.table.tBodies[0].appendChild(this.options.lineFormat.call(this.table,e,i))}this.options.afterRefresh.call(this.table)}},setOption:function(t,e){t in this.options&&(this.options[t]=e,"sort"===t&&(this.destroySort(),this.createSort(),this.triggerSort()),"sortKey"!==t&&"sortDir"!==t||this.sort(),"filters"===t&&(this.destroyFilter(),this.createFilter()),"filterText"===t&&this.changePlaceHolder(),this.filter())},setOptions:function(t){for(var e in t)e in this.options&&(this.options[e]=t[e]);"sort"in t?(this.destroySort(),this.createSort(),this.triggerSort()):("sortKey"in t||"sortDir"in t)&&this.sort(),"filters"in t&&(this.destroyFilter(),this.createFilter()),"filterText"in t&&this.changePlaceHolder(),this.filter()},destroy:function(){void 0!==this.refreshTimeOut&&clearTimeout(this.refreshTimeOut),this.destroySort();for(var t=0;t<this.pagingDivs.length;++t)this.pagingDivs[t].classList.remove("pagination-datatable"),this.pagingDivs[t].classList.remove(this.options.pagingDivClass),this.pagingDivs[t].innerHTML="";this.destroyFilter(),this.table.classList.remove(this.options.tableClass),this.removeNode(this.table.tBodies[0]),this.table.appendChild(document.createElement("tbody"));for(t=0;t<this.data.length;t++){var e=this.filterIndex[this.currentStart+t],i=this.data[e];this.table.tBodies[0].appendChild(this.options.lineFormat.call(this.table,e,i))}}},DataTable.defaultOptions={forceStrings:!1,tableClass:"datatable",pagingDivSelector:".paging",pagingDivClass:"text-center",pagingListClass:"pagination",pagingItemClass:"",pagingLinkClass:"",pagingLinkHref:"",pagingLinkDisabledTabIndex:!1,counterDivSelector:".counter",loadingDivSelector:".loading",sort:!1,sortKey:!1,sortDir:"asc",nbColumns:-1,pageSize:20,pagingNumberOfPages:9,identify:!1,onChange:function(t,e){},counterText:function(t,e,i,s,a,n){var r="Page "+t+" on "+e+". Showing "+i+" to "+s+" of "+a+" entries";return a!=n&&(r+=" (filtered from "+n+" total entries)"),r+="."},firstPage:"&lt;&lt;",prevPage:"&lt;",pagingPages:!1,nextPage:"&gt;",lastPage:"&gt;&gt;",dataTypes:!0,filters:{},filterText:"Search... ",filterEmptySelect:"",filterSelectOptions:!1,filterInputClass:"form-control",filterSelectClass:"form-control",beforeRefresh:function(){},afterRefresh:function(){},lineFormat:function(t,e){var i=document.createElement("tr");for(var s in i.dataset.id=t,e)e.hasOwnProperty(s)&&(i.innerHTML+="<td>"+e[s]+"</td>");return i}},DataTable.defaultAjaxOptions={url:null,size:null,refresh:!1,allInOne:!1,timeout:2e3};
