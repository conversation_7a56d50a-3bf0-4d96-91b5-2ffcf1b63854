body {
    font-family: Cairo, sans-serif !important; /* Added sans-serif fallback */
    background-color: #f8f9fa;
}

.card {
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease-in-out;
    border: none;
    margin-bottom: 1rem;
    background: white;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: #2c3e50;
    font-weight: 600;
}

.card-subtitle {
    color: #6c757d;
}

.text-primary {
    color: #4361ee !important; /* MDB primary might be different, this ensures override */
}

.chart-container {
    position: relative;
    height: 50vh; /* Consider using min-height or aspect-ratio for better responsiveness */
    width: 100%;
    margin: 1rem auto;
    padding: 1rem;
}

.fas { /* Font Awesome icon color */
    color: #4361ee;
}

@media (max-width: 768px) {
    .chart-container {
        height: 40vh;
    }
}

h3.card-title { /* Specific styling for h3 elements with class card-title */
    font-size: 2rem;
    font-weight: 700;
    color: #4361ee; /* Overrides general .card-title color for these specific h3s */
}

.container-fluid {
    padding: 2rem;
}