@model TajneedApp.Models.AccountViewModels.LoginViewModel

@{
    ViewData["Title"] = "تسجيل الدخول - نظام إدارة التجنيد";
    Layout = "_LoginLayout";
}

<div class="login-page-wrapper">
    <!-- Background Elements -->
    <div class="login-background-elements">
        <div class="bg-element bg-element-1"></div>
        <div class="bg-element bg-element-2"></div>
        <div class="bg-element bg-element-3"></div>
    </div>

    <div class="login-container">
        <div class="login-card-wrapper">
            <div class="login-card">
                <!-- Header Section -->
                <div class="login-header">
                    <div class="logo-section">
                        <div class="logo-container">
                            <div class="logo-overlay">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                        </div>
                        <div class="brand-info">
                            <h1 class="brand-title">نظام إدارة التجنيد</h1>
                            <p class="brand-subtitle">القوات الجوية الملكية السعودية</p>
                        </div>
                    </div>
                    <div class="welcome-message">
                        <h2 class="welcome-title">مرحباً بك</h2>
                        <p class="welcome-subtitle">أدخل بيانات الدخول للوصول إلى النظام</p>
                    </div>
                </div>

                <!-- Form Section -->
                <div class="login-form-section">
                    <form method="post" asp-controller="Account" asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]" class="login-form">

                        @if (!ViewData.ModelState.IsValid)
                        {
                            <div class="alert alert-danger alert-modern" role="alert">
                                <div class="alert-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="alert-content">
                                    <h6 class="alert-title">خطأ في تسجيل الدخول</h6>
                                    <div asp-validation-summary="All" class="alert-messages"></div>
                                </div>
                            </div>
                        }

                        <!-- Service Number Field -->
                        <div class="form-group mb-4">
                            <label asp-for="ServiceNumber" class="form-label fw-bold text-dark mb-2">
                                <i class="fas fa-id-card me-2 text-primary"></i>
                                @Html.DisplayNameFor(m => m.ServiceNumber)
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0">
                                    <i class="fas fa-user text-muted"></i>
                                </span>
                                <input asp-for="ServiceNumber"
                                       class="form-control border-start-0 ps-0"
                                       placeholder="أدخل الرقم العسكري"
                                       autocomplete="username"
                                       aria-required="true"
                                       dir="ltr"
                                       style="font-size: 1.1rem; padding: 12px;" />
                            </div>
                            <span asp-validation-for="ServiceNumber" class="text-danger small"></span>
                        </div>

                        <!-- Password Field -->
                        <div class="form-group mb-4">
                            <label asp-for="Password" class="form-label fw-bold text-dark mb-2">
                                <i class="fas fa-lock me-2 text-primary"></i>
                                @Html.DisplayNameFor(m => m.Password)
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0">
                                    <i class="fas fa-key text-muted"></i>
                                </span>
                                <input asp-for="Password"
                                       class="form-control border-start-0 border-end-0 ps-0"
                                       type="password"
                                       placeholder="أدخل كلمة المرور"
                                       autocomplete="current-password"
                                       aria-required="true"
                                       dir="ltr"
                                       style="font-size: 1.1rem; padding: 12px;"
                                       id="passwordField" />
                                <button type="button" class="btn btn-outline-secondary border-start-0" onclick="togglePassword()" style="border-color: #ced4da;">
                                    <i class="fas fa-eye" id="password-eye"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Password" class="text-danger small"></span>
                        </div>

                        <!-- Remember Me -->
                        <div class="form-group mb-4">
                            <div class="form-check">
                                <input asp-for="RememberMe" type="checkbox" class="form-check-input" />
                                <label asp-for="RememberMe" class="form-check-label text-dark">
                                    @Html.DisplayNameFor(m => m.RememberMe)
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary btn-lg w-100" style="padding: 12px; font-size: 1.1rem; font-weight: 600;">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Footer Section -->
                <div class="login-footer">
                    <div class="security-badge">
                        <i class="fas fa-shield-check"></i>
                        <span>اتصال آمن ومشفر</span>
                    </div>
                    <div class="version-info">
                        <span>الإصدار 2.0</span>
                    </div>
                </div>
            </div>

            <!-- Side Panel -->
            <div class="side-panel">
                <div class="side-content">
                    <div class="side-icon">
                        <i class="fas fa-plane"></i>
                    </div>
                    <h3 class="side-title">نظام متطور وآمن</h3>
                    <p class="side-description">
                        نظام إدارة التجنيد المتطور للقوات الجوية الملكية السعودية
                        يوفر أعلى معايير الأمان والحماية لبياناتك
                    </p>
                    <div class="side-features">
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>حماية متقدمة للبيانات</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>واجهة سهلة الاستخدام</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>دعم فني متواصل</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.getElementById('passwordField');
            const eyeIcon = document.getElementById('password-eye');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }

        // Form submission with loading state
        document.querySelector('.login-form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';

            // Re-enable after 5 seconds if form doesn't submit
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }
            }, 5000);
        });

        // Animate elements on page load
        window.addEventListener('load', function() {
            document.querySelector('.login-card').classList.add('animate-in');
            document.querySelector('.side-panel').classList.add('animate-in');

            // Animate background elements
            document.querySelectorAll('.bg-element').forEach((element, index) => {
                setTimeout(() => {
                    element.classList.add('animate');
                }, index * 200);
            });
        });

        // Add focus effects to input groups
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.closest('.input-group').style.boxShadow = '0 0 0 0.2rem rgba(13, 110, 253, 0.25)';
                this.closest('.input-group').style.borderColor = '#86b7fe';
            });

            input.addEventListener('blur', function() {
                this.closest('.input-group').style.boxShadow = 'none';
                this.closest('.input-group').style.borderColor = '';
            });
        });
    </script>
}
