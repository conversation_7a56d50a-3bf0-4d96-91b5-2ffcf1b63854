@model TajneedApp.Models.AccountViewModels.LoginViewModel

@{
    ViewData["Title"] = "تسجيل الدخول - نظام إدارة التجنيد";
    Layout = "_LoginLayout";
}

<div class="container-fluid vh-100 d-flex align-items-center justify-content-center bg-primary bg-gradient">
    <div class="row w-100 justify-content-center">
        <div class="col-12 col-md-10 col-lg-8 col-xl-6">
            <div class="card shadow-lg border-0 rounded-4 overflow-hidden">
                <div class="row g-0">
                    <!-- Login Form Section -->
                    <div class="col-md-7">
                        <div class="card-body p-5">
                            <!-- Header -->
                            <div class="text-center mb-4">
                                <div class="d-flex align-items-center justify-content-center mb-3">
                                    <div class="bg-primary bg-gradient rounded-circle p-3 me-3">
                                        <i class="fas fa-shield-alt text-white fs-2"></i>
                                    </div>
                                    <div class="text-end">
                                        <h3 class="fw-bold text-dark mb-1">نظام إدارة التجنيد</h3>
                                        <p class="text-muted small mb-0">القوات الجوية الملكية السعودية</p>
                                    </div>
                                </div>
                                <h4 class="fw-bold text-primary mb-2">مرحباً بك</h4>
                                <p class="text-muted">أدخل بيانات الدخول للوصول إلى النظام</p>
                            </div>

                            <!-- Login Form -->
                            <form method="post" asp-controller="Account" asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]">

                                <!-- Validation Summary -->
                                @if (!ViewData.ModelState.IsValid)
                                {
                                    <div class="alert alert-danger d-flex align-items-center mb-4" role="alert">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <div>
                                            <strong>خطأ في تسجيل الدخول</strong>
                                            <div asp-validation-summary="All" class="mb-0 mt-1"></div>
                                        </div>
                                    </div>
                                }

                                <!-- Service Number Field -->
                                <div class="mb-4">
                                    <label asp-for="ServiceNumber" class="form-label fw-semibold text-dark">
                                        <i class="fas fa-id-card text-primary me-2"></i>
                                        @Html.DisplayNameFor(m => m.ServiceNumber)
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-user text-muted"></i>
                                        </span>
                                        <input asp-for="ServiceNumber"
                                               class="form-control border-start-0 ps-2"
                                               placeholder="أدخل الرقم العسكري"
                                               autocomplete="username"
                                               aria-required="true"
                                               dir="ltr" />
                                    </div>
                                    <span asp-validation-for="ServiceNumber" class="text-danger small"></span>
                                </div>

                                <!-- Password Field -->
                                <div class="mb-4">
                                    <label asp-for="Password" class="form-label fw-semibold text-dark">
                                        <i class="fas fa-lock text-primary me-2"></i>
                                        @Html.DisplayNameFor(m => m.Password)
                                    </label>
                                    <div class="input-group input-group-lg">
                                        <span class="input-group-text bg-light border-end-0">
                                            <i class="fas fa-key text-muted"></i>
                                        </span>
                                        <input asp-for="Password"
                                               class="form-control border-start-0 border-end-0 ps-2"
                                               type="password"
                                               placeholder="أدخل كلمة المرور"
                                               autocomplete="current-password"
                                               aria-required="true"
                                               dir="ltr"
                                               id="passwordField" />
                                        <button type="button" class="btn btn-outline-light border-start-0" onclick="togglePassword()">
                                            <i class="fas fa-eye text-muted" id="password-eye"></i>
                                        </button>
                                    </div>
                                    <span asp-validation-for="Password" class="text-danger small"></span>
                                </div>

                                <!-- Remember Me -->
                                <div class="mb-4">
                                    <div class="form-check">
                                        <input asp-for="RememberMe" type="checkbox" class="form-check-input" />
                                        <label asp-for="RememberMe" class="form-check-label text-dark">
                                            @Html.DisplayNameFor(m => m.RememberMe)
                                        </label>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="d-grid mb-4">
                                    <button type="submit" class="btn btn-primary btn-lg fw-semibold py-3">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        تسجيل الدخول
                                    </button>
                                </div>
                            </form>

                            <!-- Footer -->
                            <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                                <div class="d-flex align-items-center text-success">
                                    <i class="fas fa-shield-check me-2"></i>
                                    <small>اتصال آمن ومشفر</small>
                                </div>
                                <small class="text-muted">الإصدار 2.0</small>
                            </div>
                        </div>
                    </div>

                    <!-- Side Panel -->
                    <div class="col-md-5 bg-primary bg-gradient text-white d-flex align-items-center">
                        <div class="p-5 text-center">
                            <div class="mb-4">
                                <i class="fas fa-plane display-1 opacity-75"></i>
                            </div>
                            <h3 class="fw-bold mb-3">نظام متطور وآمن</h3>
                            <p class="mb-4 opacity-90">
                                نظام إدارة التجنيد المتطور للقوات الجوية الملكية السعودية
                                يوفر أعلى معايير الأمان والحماية لبياناتك
                            </p>
                            <div class="text-end">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-check-circle text-success me-3"></i>
                                    <span>حماية متقدمة للبيانات</span>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-check-circle text-success me-3"></i>
                                    <span>واجهة سهلة الاستخدام</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-3"></i>
                                    <span>دعم فني متواصل</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.getElementById('passwordField');
            const eyeIcon = document.getElementById('password-eye');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }

        // Form submission with loading state
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';

            // Re-enable after 5 seconds if form doesn't submit
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }
            }, 5000);
        });

        // Enhanced input focus effects
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.closest('.input-group').classList.add('shadow-sm');
                this.closest('.input-group').style.borderColor = '#0d6efd';
            });

            input.addEventListener('blur', function() {
                this.closest('.input-group').classList.remove('shadow-sm');
                this.closest('.input-group').style.borderColor = '';
            });
        });
    </script>
}
