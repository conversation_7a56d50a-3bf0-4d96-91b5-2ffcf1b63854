@model TajneedApp.Models.AccountViewModels.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول";
    Layout = "_LoginLayout";
}

<div class="container-fluid vh-100 d-flex align-items-center justify-content-center" style="background-color: #f6f8fa;">
    <div class="row w-100 justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0" style="border-radius: 15px;">
                <div class="card-header text-center py-4" style="background: linear-gradient(135deg, #2340a0, #3e5ed7); border-radius: 15px 15px 0 0;">
                    <div class="mb-3">
                        <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-white"
                             style="width: 80px; height: 80px;">
                            <i class="ri-shield-star-line text-primary" style="font-size: 2.5rem;"></i>
                        </div>
                    </div>
                    <h2 class="text-white mb-2">نظام التجنيد</h2>
                    <p class="text-white-50 mb-0">أدخل بيانات الدخول للوصول إلى النظام</p>
                </div>

                <div class="card-body p-4">
                    <form method="post" asp-controller="Account" asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]">
                        <div asp-validation-summary="All" class="alert alert-danger" role="alert"></div>

                        <div class="mb-3">
                            <label asp-for="ServiceNumber" class="form-label fw-bold">
                                <i class="ri-user-line me-2 text-primary"></i>
                                الرقم العسكري
                            </label>
                            <input asp-for="ServiceNumber" class="form-control form-control-lg"
                                   placeholder="أدخل الرقم العسكري"
                                   autocomplete="username" aria-required="true" dir="ltr" />
                            <span asp-validation-for="ServiceNumber" class="text-danger small"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label fw-bold">
                                <i class="ri-lock-line me-2 text-primary"></i>
                                كلمة المرور
                            </label>
                            <input asp-for="Password" class="form-control form-control-lg"
                                   placeholder="أدخل كلمة المرور"
                                   autocomplete="current-password" aria-required="true" dir="ltr" />
                            <span asp-validation-for="Password" class="text-danger small"></span>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input asp-for="RememberMe" class="form-check-input" type="checkbox" />
                                <label asp-for="RememberMe" class="form-check-label">
                                    تذكرني
                                </label>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-lg"
                                    style="background: linear-gradient(135deg, #2340a0, #3e5ed7); border: none; color: white;">
                                <span class="me-2">تسجيل الدخول</span>
                                <i class="ri-arrow-left-line"></i>
                            </button>
                        </div>
                    </form>
                </div>

                <div class="card-footer text-center py-3 bg-light" style="border-radius: 0 0 15px 15px;">
                    <small class="text-muted">
                        <i class="ri-shield-check-line me-1 text-success"></i>
                        نظام آمن ومحمي
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
