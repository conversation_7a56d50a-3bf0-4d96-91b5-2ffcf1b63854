@model TajneedApp.Models.AccountViewModels.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول";
    Layout = "_LoginLayout";
}

<div class="login-container">
    <div class="login-background">
        <div class="login-card">
            <div class="login-header">
                <div class="logo-container">
                    <img src="~/images/rafo_logo.png" alt="Logo" class="logo-image">
                    <i class="ri-shield-star-line"></i>
                </div>
                <h1 class="login-title">نظام التجنيد</h1>
                <p class="login-subtitle">أدخل بيانات الدخول للوصول إلى النظام</p>
            </div>

            <div class="login-form-container">
                <form method="post" asp-controller="Account" asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]" class="login-form">
                    <div asp-validation-summary="All" class="alert alert-danger validation-summary" role="alert"></div>

                    <div class="form-group">
                        <label asp-for="ServiceNumber" class="form-label">
                            <i class="ri-user-line label-icon"></i>
                            الرقم العسكري
                        </label>
                        <div class="input-wrapper">
                            <input asp-for="ServiceNumber" class="form-control modern-input"
                                   placeholder="أدخل الرقم العسكري"
                                   autocomplete="username" aria-required="true" dir="ltr" />
                            <div class="input-focus-border"></div>
                        </div>
                        <span asp-validation-for="ServiceNumber" class="field-validation-error"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Password" class="form-label">
                            <i class="ri-lock-line label-icon"></i>
                            كلمة المرور
                        </label>
                        <div class="input-wrapper">
                            <input asp-for="Password" class="form-control modern-input"
                                   placeholder="أدخل كلمة المرور"
                                   autocomplete="current-password" aria-required="true" dir="ltr" />
                            <div class="input-focus-border"></div>
                        </div>
                        <span asp-validation-for="Password" class="field-validation-error"></span>
                    </div>

                    <div class="form-group remember-me-group">
                        <div class="custom-checkbox">
                            <input asp-for="RememberMe" class="checkbox-input" type="checkbox" />
                            <label asp-for="RememberMe" class="checkbox-label">
                                <span class="checkbox-custom"></span>
                                <span class="checkbox-text">تذكرني</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group submit-group">
                        <button type="submit" class="btn btn-login">
                            <span class="btn-text">تسجيل الدخول</span>
                            <i class="ri-arrow-left-line btn-icon"></i>
                        </button>
                    </div>
                </form>
            </div>

            <div class="login-footer">
                <p class="footer-text">
                    <i class="ri-shield-check-line"></i>
                    نظام آمن ومحمي
                </p>
            </div>
        </div>
    </div>
</div>
