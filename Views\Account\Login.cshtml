@model TajneedApp.Models.AccountViewModels.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول";
    Layout = "_LoginLayout";
}


<div class="containe">
    <div class="row">
                <div class="login-header">
            <div class="logo-container">
                <i class="ri-shield-star-line"></i>
            </div>
            <h1>نظام التجنيد</h1>
            <p class="text-muted">أدخل بيانات الدخول</p>
        </div>

        <div class="col-6">
  <form method="post" asp-controller="Account" asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]">
            <div asp-validation-summary="All" class="text-danger mb-3"></div>

            <div class="form-group">
                <label asp-for="ServiceNumber" class="form-label">الرقم العسكري</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="ri-user-line"></i>
                    </span>
                    <input asp-for="ServiceNumber" class="form-control" placeholder="أدخل الرقم العسكري"
                        autocomplete="username" aria-required="true" dir="ltr" />
                </div>
                <span asp-validation-for="ServiceNumber" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Password" class="form-label">كلمة المرور</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="ri-lock-line"></i>
                    </span>
                    <input asp-for="Password" class="form-control" placeholder="أدخل كلمة المرور"
                        autocomplete="current-password" aria-required="true" dir="ltr" />
                </div>
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>

            <div class="form-group">
                <div class="form-check">
                    <input asp-for="RememberMe" class="form-check-input" />
                    <label asp-for="RememberMe" class="form-check-label">تذكرني</label>
                </div>
            </div>

            <div class="form-group mb-0">
                <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
            </div>
        </form>
        </div>
    </div>
</div>
