@model TajneedApp.Models.AccountViewModels.LoginViewModel

@{
    ViewData["Title"] = "تسجيل الدخول - نظام إدارة التجنيد";
    Layout = "_LoginLayout";
}

<div class="login-page-wrapper">
    <!-- Background Elements -->
    <div class="login-background-elements">
        <div class="bg-element bg-element-1"></div>
        <div class="bg-element bg-element-2"></div>
        <div class="bg-element bg-element-3"></div>
    </div>

    <div class="login-container">
        <div class="login-card-wrapper">
            <div class="login-card">
                <!-- Header Section -->
                <div class="login-header">
                    <div class="logo-section">
                        <div class="logo-container">
                            <div class="logo-overlay">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                        </div>
                        <div class="brand-info">
                            <h1 class="brand-title">نظام إدارة التجنيد</h1>
                            <p class="brand-subtitle">القوات الجوية الملكية السعودية</p>
                        </div>
                    </div>
                    <div class="welcome-message">
                        <h2 class="welcome-title">مرحباً بك</h2>
                        <p class="welcome-subtitle">أدخل بيانات الدخول للوصول إلى النظام</p>
                    </div>
                </div>

                <!-- Form Section -->
                <div class="login-form-section">
                    <form method="post" asp-controller="Account" asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]" class="login-form">

                        @if (!ViewData.ModelState.IsValid)
                        {
                            <div class="alert alert-danger alert-modern" role="alert">
                                <div class="alert-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="alert-content">
                                    <h6 class="alert-title">خطأ في تسجيل الدخول</h6>
                                    <div asp-validation-summary="All" class="alert-messages"></div>
                                </div>
                            </div>
                        }

                        <!-- Service Number Field -->
                        <div class="form-group-modern">
                            <div class="input-container">
                                <div class="input-icon">
                                    <i class="fas fa-id-card"></i>
                                </div>
                                <div class="input-field">
                                    <input asp-for="ServiceNumber"
                                           class="form-control-modern"
                                           placeholder=" "
                                           autocomplete="username"
                                           aria-required="true"
                                           dir="ltr" />
                                    <label asp-for="ServiceNumber" class="floating-label">
                                        @Html.DisplayNameFor(m => m.ServiceNumber)
                                    </label>
                                    <div class="input-border"></div>
                                </div>
                            </div>
                            <span asp-validation-for="ServiceNumber" class="field-validation-modern"></span>
                        </div>

                        <!-- Password Field -->
                        <div class="form-group-modern">
                            <div class="input-container">
                                <div class="input-icon">
                                    <i class="fas fa-lock"></i>
                                </div>
                                <div class="input-field">
                                    <input asp-for="Password"
                                           class="form-control-modern"
                                           type="password"
                                           placeholder=" "
                                           autocomplete="current-password"
                                           aria-required="true"
                                           dir="ltr" />
                                    <label asp-for="Password" class="floating-label">
                                        @Html.DisplayNameFor(m => m.Password)
                                    </label>
                                    <div class="input-border"></div>
                                    <button type="button" class="password-toggle" onclick="togglePassword()">
                                        <i class="fas fa-eye" id="password-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <span asp-validation-for="Password" class="field-validation-modern"></span>
                        </div>

                        <!-- Remember Me -->
                        <div class="form-group-modern remember-section">
                            <div class="custom-checkbox-modern">
                                <input asp-for="RememberMe" type="checkbox" class="checkbox-modern" />
                                <label asp-for="RememberMe" class="checkbox-label-modern">
                                    <span class="checkbox-indicator"></span>
                                    <span class="checkbox-text">@Html.DisplayNameFor(m => m.RememberMe)</span>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-group-modern submit-section">
                            <button type="submit" class="btn-login-modern">
                                <span class="btn-content">
                                    <span class="btn-text">تسجيل الدخول</span>
                                    <span class="btn-icon">
                                        <i class="fas fa-arrow-left"></i>
                                    </span>
                                </span>
                                <div class="btn-loading">
                                    <div class="spinner"></div>
                                </div>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Footer Section -->
                <div class="login-footer">
                    <div class="security-badge">
                        <i class="fas fa-shield-check"></i>
                        <span>اتصال آمن ومشفر</span>
                    </div>
                    <div class="version-info">
                        <span>الإصدار 2.0</span>
                    </div>
                </div>
            </div>

            <!-- Side Panel -->
            <div class="side-panel">
                <div class="side-content">
                    <div class="side-icon">
                        <i class="fas fa-plane"></i>
                    </div>
                    <h3 class="side-title">نظام متطور وآمن</h3>
                    <p class="side-description">
                        نظام إدارة التجنيد المتطور للقوات الجوية الملكية السعودية
                        يوفر أعلى معايير الأمان والحماية لبياناتك
                    </p>
                    <div class="side-features">
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>حماية متقدمة للبيانات</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>واجهة سهلة الاستخدام</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>دعم فني متواصل</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.querySelector('input[name="Password"]');
            const eyeIcon = document.getElementById('password-eye');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }

        // Form submission with loading state
        document.querySelector('.login-form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('.btn-login-modern');
            const btnContent = submitBtn.querySelector('.btn-content');
            const btnLoading = submitBtn.querySelector('.btn-loading');

            submitBtn.disabled = true;
            btnContent.style.opacity = '0';
            btnLoading.style.opacity = '1';

            // Re-enable after 3 seconds if form doesn't submit
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.disabled = false;
                    btnContent.style.opacity = '1';
                    btnLoading.style.opacity = '0';
                }
            }, 3000);
        });

        // Input focus animations
        document.querySelectorAll('.form-control-modern').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });

            // Check if input has value on page load
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        });

        // Animate elements on page load
        window.addEventListener('load', function() {
            document.querySelector('.login-card').classList.add('animate-in');
            document.querySelector('.side-panel').classList.add('animate-in');

            // Animate background elements
            document.querySelectorAll('.bg-element').forEach((element, index) => {
                setTimeout(() => {
                    element.classList.add('animate');
                }, index * 200);
            });
        });
    </script>
}
