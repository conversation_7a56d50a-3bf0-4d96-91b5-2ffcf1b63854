@model IEnumerable<TajneedApp.Models.Rank>

@{
    ViewData["Title"] = "إدارة الرتب";
}

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary fw-bold mb-1">
                        <i class="fas fa-list me-2"></i>إدارة الرتب العسكرية
                    </h2>
                    <p class="text-muted mb-0">عرض وإدارة جميع الرتب العسكرية في النظام</p>
                </div>
                <div>
                    <a asp-action="Create" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>إضافة رتبة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>



    <!-- Main Table Card -->
    <div class="card shadow border-0">
        <div class="card-header bg-primary text-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>جدول الرتب العسكرية
                    </h5>
                </div>
                <div class="col-auto">
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-list me-1"></i>@Model.Count() رتبة
                    </span>
                </div>
            </div>
        </div>

        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="ranksDataTable">
                    <thead class="table-dark">
                        <tr>
                            <th class="text-center" style="min-width: 80px;">
                                <i class="fas fa-hashtag me-1"></i>الرقم
                            </th>
                            <th class="text-center" style="min-width: 200px;">
                                <i class="fas fa-list me-1"></i>اسم الرتبة
                            </th>
                            <th class="text-center no-sort" style="min-width: 150px;">
                                <i class="fas fa-cogs me-1"></i>الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td class="text-center">
                                    <span class="badge bg-secondary">@item.RankId</span>
                                </td>
                                <td class="fw-bold text-center">
                                    @Html.DisplayFor(modelItem => item.RankName)
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.RankId"
                                           class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@item.RankId"
                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@item.RankId"
                                           class="btn btn-sm btn-outline-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذه الرتبة؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Initialize simple DataTable
            var table = $('#ranksDataTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[5, 10, 25], [5, 10, 25]],
                "order": [[1, 'asc']], // Sort by Rank Name by default
                "columnDefs": [
                    {
                        "targets": [-1], // Actions column
                        "orderable": false,
                        "searchable": false
                    }
                ]
            });

        });
    </script>
}
