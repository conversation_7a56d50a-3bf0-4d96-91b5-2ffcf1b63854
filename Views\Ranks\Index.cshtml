@model IEnumerable<TajneedApp.Models.Rank>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.RankName)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.RankName)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.RankId">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.RankId">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.RankId">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
