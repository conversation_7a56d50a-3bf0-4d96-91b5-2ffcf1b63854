@model IEnumerable<TajneedApp.Models.Rank>

@{
    ViewData["Title"] = "إدارة الرتب";
}

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary fw-bold mb-1">
                        <i class="fas fa-list me-2"></i>إدارة الرتب العسكرية
                    </h2>
                    <p class="text-muted mb-0">عرض وإدارة جميع الرتب العسكرية في النظام</p>
                </div>
                <div>
                    <a asp-action="Create" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>إضافة رتبة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>



    <!-- Main Table Card -->
    <div class="card shadow border-0">
        <div class="card-header bg-primary text-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>جدول الرتب العسكرية
                    </h5>
                </div>
                <div class="col-auto">
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-star me-1"></i>@Model.Count() رتبة
                    </span>
                </div>
            </div>
        </div>

        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="ranksDataTable">
                    <thead class="table-dark">
                        <tr>
                            <th class="text-center" style="min-width: 80px;">
                                <i class="fas fa-hashtag me-1"></i>الرقم
                            </th>
                            <th class="text-center" style="min-width: 200px;">
                                <i class="fas fa-list me-1"></i>اسم الرتبة
                            </th>
                            <th class="text-center no-sort" style="min-width: 150px;">
                                <i class="fas fa-cogs me-1"></i>الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td class="text-center">
                                    <span class="badge bg-secondary">@item.RankId</span>
                                </td>
                                <td class="fw-bold text-center">
                                    @Html.DisplayFor(modelItem => item.RankName)
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.RankId"
                                           class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@item.RankId"
                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@item.RankId"
                                           class="btn btn-sm btn-outline-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذه الرتبة؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Initialize DataTable with enhanced features
            var table = $('#ranksDataTable').DataTable({
                "scrollX": true,
                "language": {
                    "url": "/localization/ar.json"
                },
                "responsive": {
                    "details": {
                        "type": 'column',
                        "target": 'tr'
                    }
                },
                "autoWidth": false,
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
                "dom": 'Bfrtip',
                "buttons": [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> تصدير Excel',
                        className: 'btn btn-success btn-sm',
                        exportOptions: {
                            columns: ':not(.no-export)'
                        }
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> تصدير PDF',
                        className: 'btn btn-danger btn-sm',
                        exportOptions: {
                            columns: ':not(.no-export)'
                        },
                        customize: function (doc) {
                            doc.defaultStyle.font = 'Arial';
                            doc.styles.tableHeader.alignment = 'center';
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> طباعة',
                        className: 'btn btn-info btn-sm',
                        exportOptions: {
                            columns: ':not(.no-export)'
                        }
                    },
                    {
                        extend: 'colvis',
                        text: '<i class="fas fa-columns"></i> إظهار/إخفاء الأعمدة',
                        className: 'btn btn-secondary btn-sm'
                    }
                ],
                "columnDefs": [
                    {
                        "targets": [0], // Rank ID
                        "width": "80px",
                        "className": "text-center"
                    },
                    {
                        "targets": [1], // Rank Name
                        "width": "200px",
                        "className": "text-center"
                    },
                    {
                        "targets": [-1], // Actions column
                        "orderable": false,
                        "searchable": false,
                        "width": "150px",
                        "className": "text-center no-export"
                    }
                ],
                "order": [[1, 'asc']], // Sort by Rank Name by default
                "searching": true,
                "ordering": true,
                "info": true,
                "paging": true,
                "stateSave": true,
                "fixedHeader": true
            });

            // Add loading overlay
            table.on('processing.dt', function (e, settings, processing) {
                if (processing) {
                    $('.table-responsive').append('<div class="loading-overlay"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
                } else {
                    $('.loading-overlay').remove();
                }
            });

            // Responsive table adjustments
            $(window).on('resize', function () {
                table.columns.adjust().responsive.recalc();
            });

            // Add row click functionality for mobile
            $('#ranksDataTable tbody').on('click', 'tr', function () {
                if ($(window).width() < 768) {
                    $(this).toggleClass('selected');
                }
            });

            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();

            // Enhanced delete confirmation
            $('a[onclick*="confirm"]').on('click', function(e) {
                e.preventDefault();
                var href = $(this).attr('href');

                Swal.fire({
                    title: 'تأكيد الحذف',
                    text: 'هل أنت متأكد من حذف هذه الرتبة؟ لا يمكن التراجع عن هذا الإجراء.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'نعم، احذف',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = href;
                    }
                });
            });
        });

        // Additional UI enhancements
        setTimeout(function() {
            // Style DataTable elements
            $('.dataTables_filter input').addClass('form-control form-control-sm');
            $('.dataTables_length select').addClass('form-select form-select-sm');
            $('.dataTables_filter input').attr('placeholder', 'البحث في الرتب...');
        }, 500);
    </script>
}
