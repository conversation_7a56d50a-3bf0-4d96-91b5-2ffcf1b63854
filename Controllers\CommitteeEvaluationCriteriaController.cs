using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using TajneedApp.Data;
using TajneedApp.Models;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace TajneedApp.Controllers
{
    [Authorize]
    public class CommitteeEvaluationCriteriaController : Controller
    {
        private readonly ApplicationDbContext _context;

        public CommitteeEvaluationCriteriaController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: CommitteeEvaluationCriteria
        public async Task<IActionResult> Index(int? componentId)
        {
            var query = _context.CommitteeEvaluationCriteria
                .Include(c => c.Component)
                    .ThenInclude(comp => comp.EvaluationPath)
                        .ThenInclude(path => path.Category)
                .AsQueryable();

            if (componentId.HasValue)
            {
                query = query.Where(c => c.ComponentId == componentId.Value);
                var component = await _context.EvaluationComponents
                    .Include(c => c.EvaluationPath)
                        .ThenInclude(p => p.Category)
                    .FirstOrDefaultAsync(c => c.ComponentId == componentId.Value);
                ViewData["EvaluationComponent"] = component;
            }

            var criteria = await query
                .OrderBy(c => c.Component.EvaluationPath.Category.CategoryName)
                .ThenBy(c => c.Component.EvaluationPath.PathName)
                .ThenBy(c => c.Component.ComponentName)
                .ThenBy(c => c.DisplayOrder)
                .ToListAsync();

            return View(criteria);
        }

        // GET: CommitteeEvaluationCriteria/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var criteria = await _context.CommitteeEvaluationCriteria
                .Include(c => c.Component)
                    .ThenInclude(comp => comp.EvaluationPath)
                        .ThenInclude(path => path.Category)
                .FirstOrDefaultAsync(m => m.CriteriaId == id);

            if (criteria == null)
            {
                return NotFound();
            }

            return View(criteria);
        }

        // GET: CommitteeEvaluationCriteria/Create
        public async Task<IActionResult> Create(int? componentId)
        {
            await PopulateComponentSelectList(componentId);

            var criteria = new CommitteeEvaluationCriteria();
            if (componentId.HasValue)
            {
                criteria.ComponentId = componentId.Value;
                // Set next display order
                var maxOrder = await _context.CommitteeEvaluationCriteria
                    .Where(c => c.ComponentId == componentId.Value)
                    .MaxAsync(c => (int?)c.DisplayOrder) ?? 0;
                criteria.DisplayOrder = maxOrder + 1;
            }

            return View(criteria);
        }

        // POST: CommitteeEvaluationCriteria/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("CriteriaId,CriteriaName,CriteriaDescription,ComponentId,MaxScore,WeightPercentage,DisplayOrder,IsActive")] CommitteeEvaluationCriteria criteria)
        {
            if (ModelState.IsValid)
            {
                criteria.CreatedDate = DateTime.UtcNow;
                criteria.CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier);

                _context.Add(criteria);
                await _context.SaveChangesAsync();

                TempData["Success"] = "تم إنشاء معيار التقييم بنجاح";
                return RedirectToAction(nameof(Index), new { componentId = criteria.ComponentId });
            }

            await PopulateComponentSelectList(criteria.ComponentId);
            return View(criteria);
        }

        // GET: CommitteeEvaluationCriteria/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var criteria = await _context.CommitteeEvaluationCriteria.FindAsync(id);
            if (criteria == null)
            {
                return NotFound();
            }

            await PopulateComponentSelectList(criteria.ComponentId);
            return View(criteria);
        }

        // POST: CommitteeEvaluationCriteria/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("CriteriaId,CriteriaName,CriteriaDescription,ComponentId,MaxScore,WeightPercentage,DisplayOrder,IsActive,CreatedDate,CreatedBy")] CommitteeEvaluationCriteria criteria)
        {
            if (id != criteria.CriteriaId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(criteria);
                    await _context.SaveChangesAsync();

                    TempData["Success"] = "تم تحديث معيار التقييم بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CriteriaExists(criteria.CriteriaId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index), new { componentId = criteria.ComponentId });
            }

            await PopulateComponentSelectList(criteria.ComponentId);
            return View(criteria);
        }

        // GET: CommitteeEvaluationCriteria/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var criteria = await _context.CommitteeEvaluationCriteria
                .Include(c => c.Component)
                    .ThenInclude(comp => comp.EvaluationPath)
                        .ThenInclude(path => path.Category)
                .FirstOrDefaultAsync(m => m.CriteriaId == id);

            if (criteria == null)
            {
                return NotFound();
            }

            return View(criteria);
        }

        // POST: CommitteeEvaluationCriteria/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var criteria = await _context.CommitteeEvaluationCriteria.FindAsync(id);
            if (criteria != null)
            {
                var componentId = criteria.ComponentId;
                _context.CommitteeEvaluationCriteria.Remove(criteria);
                await _context.SaveChangesAsync();
                TempData["Success"] = "تم حذف معيار التقييم بنجاح";
                return RedirectToAction(nameof(Index), new { componentId = componentId });
            }

            return RedirectToAction(nameof(Index));
        }

        private bool CriteriaExists(int id)
        {
            return _context.CommitteeEvaluationCriteria.Any(e => e.CriteriaId == id);
        }

        private async Task PopulateComponentSelectList(int? selectedComponentId = null)
        {
            var components = await _context.EvaluationComponents
                .Include(c => c.EvaluationPath)
                    .ThenInclude(p => p.Category)
                .Where(c => c.ComponentType == ComponentType.CommitteeEvaluation && c.IsActive)
                .OrderBy(c => c.EvaluationPath.Category.CategoryName)
                .ThenBy(c => c.EvaluationPath.PathName)
                .ThenBy(c => c.ComponentName)
                .ToListAsync();

            ViewData["ComponentId"] = new SelectList(
                components.Select(c => new { 
                    c.ComponentId, 
                    DisplayText = $"{c.EvaluationPath?.Category?.CategoryName} - {c.EvaluationPath?.PathName} - {c.ComponentName}" 
                }), 
                "ComponentId", 
                "DisplayText", 
                selectedComponentId);
        }

        // AJAX: Get criteria for a component
        [HttpGet]
        public async Task<IActionResult> GetCriteriaForComponent(int componentId)
        {
            var criteria = await _context.CommitteeEvaluationCriteria
                .Where(c => c.ComponentId == componentId && c.IsActive)
                .OrderBy(c => c.DisplayOrder)
                .Select(c => new { 
                    c.CriteriaId, 
                    c.CriteriaName, 
                    c.MaxScore, 
                    c.WeightPercentage 
                })
                .ToListAsync();

            return Json(criteria);
        }

        // POST: Reorder criteria
        [HttpPost]
        public async Task<IActionResult> ReorderCriteria([FromBody] List<int> criteriaIds)
        {
            try
            {
                for (int i = 0; i < criteriaIds.Count; i++)
                {
                    var criteria = await _context.CommitteeEvaluationCriteria.FindAsync(criteriaIds[i]);
                    if (criteria != null)
                    {
                        criteria.DisplayOrder = i + 1;
                    }
                }

                await _context.SaveChangesAsync();
                return Json(new { success = true, message = "تم تحديث ترتيب المعايير بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "حدث خطأ أثناء تحديث الترتيب" });
            }
        }

        // GET: Bulk Create Criteria
        public async Task<IActionResult> BulkCreate(int? componentId)
        {
            if (!componentId.HasValue)
            {
                return RedirectToAction(nameof(Index));
            }

            var component = await _context.EvaluationComponents
                .Include(c => c.EvaluationPath)
                    .ThenInclude(p => p.Category)
                .FirstOrDefaultAsync(c => c.ComponentId == componentId.Value);

            if (component == null)
            {
                return NotFound();
            }

            ViewData["Component"] = component;
            return View();
        }

        // POST: Bulk Create Criteria
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> BulkCreate(int componentId, string criteriaText)
        {
            if (string.IsNullOrWhiteSpace(criteriaText))
            {
                TempData["Error"] = "يرجى إدخال المعايير";
                return RedirectToAction(nameof(BulkCreate), new { componentId });
            }

            var lines = criteriaText.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            var createdCount = 0;
            var displayOrder = await _context.CommitteeEvaluationCriteria
                .Where(c => c.ComponentId == componentId)
                .MaxAsync(c => (int?)c.DisplayOrder) ?? 0;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                if (!string.IsNullOrEmpty(trimmedLine))
                {
                    var criteria = new CommitteeEvaluationCriteria
                    {
                        CriteriaName = trimmedLine,
                        CriteriaDescription = trimmedLine,
                        ComponentId = componentId,
                        MaxScore = 10, // Default value
                        WeightPercentage = Math.Round(100m / lines.Length, 2), // Equal distribution
                        DisplayOrder = ++displayOrder,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier)
                    };

                    _context.CommitteeEvaluationCriteria.Add(criteria);
                    createdCount++;
                }
            }

            await _context.SaveChangesAsync();
            TempData["Success"] = $"تم إنشاء {createdCount} معيار بنجاح";
            return RedirectToAction(nameof(Index), new { componentId });
        }
    }
}
