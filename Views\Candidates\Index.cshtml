@model IEnumerable<TajneedApp.Models.Candidate>

@{
    ViewData["Title"] = "إدارة المرشحين";
}

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary fw-bold mb-1">إدارة المرشحين</h2>
                    <p class="text-muted mb-0">عرض وإدارة جميع المرشحين في النظام</p>
                </div>
                <div>
                    <a asp-action="Create" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>إضافة مرشح جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">إجمالي المرشحين</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Count()</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">الفئات المختلفة</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Select(c => c.Category?.CategoryName).Distinct().Count()</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-layer-group fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">القواعد الجوية</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Select(c => c.Airbase?.AirbaseName).Distinct().Count()</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-plane fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">الرتب المختلفة</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.Select(c => c.Rank?.RankName).Distinct().Count()</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-medal fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Table Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="m-0 fw-bold text-primary">قائمة المرشحين</h6>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="toggleColumns">
                            <i class="fas fa-columns me-1"></i>إظهار/إخفاء الأعمدة
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="$('#candidatesDataTable').DataTable().buttons().container().find('.buttons-excel').click();">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="candidatesDataTable" class="table table-bordered table-hover table-striped" style="width:100%;">
                    <thead class="table-dark">
                        <tr>
                            <th class="text-center" style="min-width: 100px;">
                                <i class="fas fa-id-badge me-1"></i>الرقم العسكري
                            </th>
                            <th class="text-center" style="min-width: 80px;">
                                <i class="fas fa-star me-1"></i>الرتبة
                            </th>
                            <th class="text-center" style="min-width: 150px;">
                                <i class="fas fa-user me-1"></i>الاسم الكامل
                            </th>
                            <th class="text-center" style="min-width: 120px;">
                                <i class="fas fa-id-card me-1"></i>رقم الهوية
                            </th>
                            <th class="text-center" style="min-width: 100px;">
                                <i class="fas fa-building me-1"></i>القسم
                            </th>
                            <th class="text-center" style="min-width: 100px;">
                                <i class="fas fa-phone me-1"></i>الهاتف
                            </th>
                            <th class="text-center" style="min-width: 120px;">
                                <i class="fas fa-map-marker-alt me-1"></i>العنوان
                            </th>
                            <th class="text-center" style="min-width: 100px;">
                                <i class="fas fa-graduation-cap me-1"></i>التخصص
                            </th>
                            <th class="text-center" style="min-width: 120px;">
                                <i class="fas fa-university me-1"></i>الجامعة
                            </th>
                            <th class="text-center" style="min-width: 80px;">
                                <i class="fas fa-calendar me-1"></i>سنة التخرج
                            </th>
                            <th class="text-center" style="min-width: 100px;">
                                <i class="fas fa-briefcase me-1"></i>المسمى الوظيفي
                            </th>
                            <th class="text-center" style="min-width: 120px;">
                                <i class="fas fa-plane me-1"></i>القاعدة الجوية
                            </th>
                            <th class="text-center" style="min-width: 100px;">
                                <i class="fas fa-tags me-1"></i>الفئة
                            </th>
                            <th class="text-center no-sort" style="min-width: 120px;">
                                <i class="fas fa-cogs me-1"></i>الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td class="text-center">
                                    <span class="badge bg-secondary">@Html.DisplayFor(modelItem => item.ServiceNumber)</span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-primary">@(item.Rank?.RankName ?? "غير محدد")</span>
                                </td>
                                <td class="fw-bold">
                                    @Html.DisplayFor(modelItem => item.FullName)
                                </td>
                                <td class="text-center">
                                    @Html.DisplayFor(modelItem => item.NationalIdNumber)
                                </td>
                                <td class="text-center">
                                    @Html.DisplayFor(modelItem => item.Department)
                                </td>
                                <td class="text-center">
                                    <a href="tel:@item.Phone1" class="text-decoration-none">
                                        <i class="fas fa-phone text-success me-1"></i>@Html.DisplayFor(modelItem => item.Phone1)
                                    </a>
                                </td>
                                <td class="text-truncate" style="max-width: 120px;" title="@item.Address">
                                    @Html.DisplayFor(modelItem => item.Address)
                                </td>
                                <td class="text-center">
                                    @Html.DisplayFor(modelItem => item.Major)
                                </td>
                                <td class="text-center">
                                    @Html.DisplayFor(modelItem => item.University)
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-info">@Html.DisplayFor(modelItem => item.GraduationYear)</span>
                                </td>
                                <td class="text-center">
                                    @Html.DisplayFor(modelItem => item.JobTitle)
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-warning text-dark">@(item.Airbase?.AirbaseName ?? "غير محدد")</span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-success">@(item.Category?.CategoryName ?? "غير محدد")</span>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.CandidateId"
                                           class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@item.CandidateId"
                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@item.CandidateId"
                                           class="btn btn-sm btn-outline-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا المرشح؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Initialize DataTable with enhanced features
            var table = $('#candidatesDataTable').DataTable({
                "scrollX": true,
                "language": {
                    "url": "/localization/ar.json"
                },
                "responsive": {
                    "details": {
                        "type": 'column',
                        "target": 'tr'
                    }
                },
                "autoWidth": false,
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
                "dom": 'Bfrtip',
                "buttons": [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> تصدير Excel',
                        className: 'btn btn-success btn-sm',
                        exportOptions: {
                            columns: ':not(.no-export)'
                        }
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> تصدير PDF',
                        className: 'btn btn-danger btn-sm',
                        exportOptions: {
                            columns: ':not(.no-export)'
                        },
                        customize: function (doc) {
                            doc.defaultStyle.font = 'Arial';
                            doc.styles.tableHeader.alignment = 'center';
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> طباعة',
                        className: 'btn btn-info btn-sm',
                        exportOptions: {
                            columns: ':not(.no-export)'
                        }
                    },
                    {
                        extend: 'colvis',
                        text: '<i class="fas fa-columns"></i> إظهار/إخفاء الأعمدة',
                        className: 'btn btn-secondary btn-sm'
                    }
                ],
                "columnDefs": [
                    {
                        "targets": [0], // Service Number
                        "width": "100px",
                        "className": "text-center"
                    },
                    {
                        "targets": [1], // Rank
                        "width": "80px",
                        "className": "text-center"
                    },
                    {
                        "targets": [2], // Full Name
                        "width": "150px",
                        "className": "text-start"
                    },
                    {
                        "targets": [3], // National ID
                        "width": "120px",
                        "className": "text-center"
                    },
                    {
                        "targets": [4, 5, 6, 7, 8, 10, 11], // Other columns
                        "width": "100px",
                        "className": "text-center"
                    },
                    {
                        "targets": [9], // Graduation Year
                        "width": "80px",
                        "className": "text-center"
                    },
                    {
                        "targets": [-1], // Actions column
                        "orderable": false,
                        "searchable": false,
                        "width": "120px",
                        "className": "text-center no-export"
                    }
                ],
                "order": [[2, 'asc']], // Sort by Full Name by default
                "searching": true,
                "ordering": true,
                "info": true,
                "paging": true,
                "stateSave": true,
                "fixedHeader": true
            });

            // Custom search functionality
            $('#customSearch').on('keyup', function () {
                table.search(this.value).draw();
            });

            // Column visibility toggle
            $('#toggleColumns').on('click', function () {
                $('.dt-button.buttons-colvis').click();
            });

            // Add custom styling to DataTable elements
            $('.dataTables_filter input').addClass('form-control form-control-sm');
            $('.dataTables_length select').addClass('form-select form-select-sm');

            // Add search placeholder
            $('.dataTables_filter input').attr('placeholder', 'البحث في جميع الحقول...');

            // Style pagination
            $('.dataTables_paginate .paginate_button').addClass('btn btn-sm btn-outline-primary me-1');
            $('.dataTables_paginate .paginate_button.current').removeClass('btn-outline-primary').addClass('btn-primary');

            // Add loading overlay
            table.on('processing.dt', function (e, settings, processing) {
                if (processing) {
                    $('.table-responsive').append('<div class="loading-overlay"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
                } else {
                    $('.loading-overlay').remove();
                }
            });

            // Responsive table adjustments
            $(window).on('resize', function () {
                table.columns.adjust().responsive.recalc();
            });

            // Add row click functionality for mobile
            $('#candidatesDataTable tbody').on('click', 'tr', function () {
                if ($(window).width() < 768) {
                    $(this).toggleClass('selected');
                }
            });

            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();
        });

        // Additional UI enhancements
        setTimeout(function() {
            // Style DataTable elements
            $('.dataTables_filter input').addClass('form-control form-control-sm');
            $('.dataTables_length select').addClass('form-select form-select-sm');
            $('.dataTables_filter input').attr('placeholder', 'البحث في جميع الحقول...');
        }, 500);
    </script>
}
