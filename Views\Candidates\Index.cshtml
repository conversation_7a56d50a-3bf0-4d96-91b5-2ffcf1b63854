@model IEnumerable<TajneedApp.Models.Candidate>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.FullName)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.ServiceNumber)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.NationalIdNumber)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Department)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Phone1)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Phone2)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Phone3)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Major)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.University)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.GraduationYear)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.MarksGrade)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.IsActive)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Address)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.DateOfBirth)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.JobTitle)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Airbase)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Category)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Rank)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.FullName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.ServiceNumber)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.NationalIdNumber)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Department)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Phone1)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Phone2)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Phone3)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Major)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.University)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.GraduationYear)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.MarksGrade)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.IsActive)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Address)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.DateOfBirth)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.JobTitle)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Airbase.AirbaseName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Category.CategoryCode)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Rank.RankName)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.CandidateId">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.CandidateId">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.CandidateId">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
