@model IEnumerable<TajneedApp.Models.Candidate>

@{
    ViewData["Title"] = "إدارة المرشحين"; // Changed title
}

<div class="container-fluid">


    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h5 class="m-0 fw-bolder">قائمة المرشحين</h5>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة مرشح جديد
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="candidatesDataTable" class="table table-bordered table-hover table-striped table-sm" style="font-size: 13px; width:100%;">
                    <thead class="table-light">
                        <tr>
                            <th>
                                @Html.DisplayNameFor(model => model.ServiceNumber)
                            </th> 
                            <th>
                                @Html.DisplayNameFor(model => model.Rank)
                            </th>
                            
                            <th>
                                @Html.DisplayNameFor(model => model.FullName)
                            </th>
                            <th>
                                @Html.DisplayNameFor(model => model.NationalIdNumber)
                            </th>
                            <th>
                                @Html.DisplayNameFor(model => model.Department)
                            </th>
                            
                            <th>
                                @Html.DisplayNameFor(model => model.Phone1)
                            </th>
                            <th>
                                @Html.DisplayNameFor(model => model.Address)
                            </th>
                            <th>
                                @Html.DisplayNameFor(model => model.Major)
                            </th>
                            <th>
                                @Html.DisplayNameFor(model => model.University)
                            </th>
                            <th>
                                @Html.DisplayNameFor(model => model.GraduationYear)
                            </th>
                            <th>
                                @Html.DisplayNameFor(model => model.JobTitle)
                            </th>
                            <th>
                                @Html.DisplayNameFor(model => model.Airbase)
                            </th>
                            <th>
                                @Html.DisplayNameFor(model => model.Category.CategoryName)
                            </th>
                          
                            <th class="text-center">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>
                                    @Html.DisplayFor(modelItem => item.ServiceNumber)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.Rank.RankName)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.FullName)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.NationalIdNumber)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.Department)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.Phone1)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.Address)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.Major)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.University)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.GraduationYear)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.JobTitle)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.Airbase.AirbaseName)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.Category.CategoryName)
                                </td>
                               
                                <td class="text-center">
                                    <a asp-action="Edit" asp-route-id="@item.CandidateId" class="btn btn-sm btn-primary me-1" title="تعديل"><i class="fas fa-edit"></i></a>
                                    <a asp-action="Details" asp-route-id="@item.CandidateId" class="btn btn-sm btn-info me-1" title="تفاصيل"><i class="fas fa-info-circle"></i></a>
                                    <a asp-action="Delete" asp-route-id="@item.CandidateId" class="btn btn-sm btn-danger" title="حذف"><i class="fas fa-trash"></i></a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#candidatesDataTable').DataTable({
                "scrollX": true,
                "language": {
                    "url": "/localization/ar.json" // Adjust path if your ar.json is elsewhere
                },
                "responsive": true,
                "autoWidth": true,
                "columnDefs": [
                    {
                        "orderable": true,
                        "targets": -1, // Targets the last column (actions)
                        "width": "130px"  // Fixed width for actions column
                    },
                    {
                        "targets": 1, // FullName column
                        "width": "200px"
                    },
                    {
                        "targets": 10, // Airbase column
                        "width": "180px"
                    },
                    {
                        "targets": "_all", // Apply to all other columns
                        "width": "auto"
                    }
                ]
            });
        });
    </script>
}
