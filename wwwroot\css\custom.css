:root {
  --primary: #2340a0;
  --primary-light: #3e5ed7;
  --background: #f6f8fa;
  --white: #fff;
  --text: #222;
  --text-muted: #888;
  --radius: 1rem;
  --transition: 0.3s;
  --sidebar-width: 260px;
  --sidebar-width-collapsed: 64px;
}

html {
  direction: rtl;
}

body {
  margin: 0;
  font-family: 'Cairo', system-ui, sans-serif;
  background: var(--background);
  color: var(--text);
  min-height: 100vh;
}

.sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--primary);
  color: var(--white);
  display: flex;
  flex-direction: column;
  transition: width var(--transition);
  z-index: 100;
}

.sidebar.collapsed {
  width: var(--sidebar-width-collapsed);
}

.sidebar .brand {
  font-size: 1.3rem;
  font-weight: bold;
  padding: 1.5rem 1rem;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sidebar nav {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
}

.sidebar .nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--white);
  text-decoration: none;
  padding: 0.75rem 1rem;
  border-radius: var(--radius);
  transition: background var(--transition), color var(--transition);
}

.sidebar .nav-link.active,
.sidebar .nav-link:hover {
  background: var(--primary-light);
  color: var(--white);
}

.header {
  position: sticky;
  top: 0;
  right: 0;
  left: 0;
  height: 4rem;
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  z-index: 10;
}

.header .toggle-btn {
  background: none;
  border: none;
  color: var(--primary);
  font-size: 1.5rem;
  cursor: pointer;
}

.content {
  margin-right: var(--sidebar-width);
  padding: 2rem;
  transition: margin-right var(--transition);
}

.sidebar.collapsed ~ .content {
  margin-right: var(--sidebar-width-collapsed);
}

@media (max-width: 992px) {
  .sidebar {
    width: var(--sidebar-width-collapsed);
  }
  .content {
    margin-right: var(--sidebar-width-collapsed);
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .sidebar {
    right: -100vw;
    width: var(--sidebar-width);
    transition: right var(--transition);
  }
  .sidebar.open {
    right: 0;
  }
  .content {
    margin-right: 0;
    padding: 1rem;
  }
  .header {
    padding: 0 1rem;
  }
}

/* Forms */
input, select, textarea {
  font-family: inherit;
  font-size: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 1rem;
  background: var(--white);
  color: var(--text);
  transition: border-color var(--transition);
}

input:focus, select:focus, textarea:focus {
  border-color: var(--primary);
  outline: none;
}

button, .btn {
  background: var(--primary);
  color: var(--white);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background var(--transition), transform var(--transition);
}

button:hover, .btn:hover {
  background: var(--primary-light);
  transform: translateY(-2px);
}