:root {
  --primary: #2340a0;
  --primary-light: #3e5ed7;
  --background: #f6f8fa;
  --white: #fff;
  --text: #222;
  --text-muted: #888;
  --radius: 1rem;
  --transition: 0.3s;
  --sidebar-width: 260px;
  --sidebar-width-collapsed: 64px;
}

html {
  direction: rtl;
}

body {
  margin: 0;
  font-family: 'Cairo', system-ui, sans-serif;
  background: var(--background);
  color: var(--text);
  min-height: 100vh;
}

.sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--primary);
  color: var(--white);
  display: flex;
  flex-direction: column;
  transition: width var(--transition);
  z-index: 100;
}

.sidebar.collapsed {
  width: var(--sidebar-width-collapsed);
}

.sidebar .brand {
  font-size: 1.3rem;
  font-weight: bold;
  padding: 1.5rem 1rem;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sidebar nav {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
}

.sidebar .nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--white);
  text-decoration: none;
  padding: 0.75rem 1rem;
  border-radius: var(--radius);
  transition: background var(--transition), color var(--transition);
}

.sidebar .nav-link.active,
.sidebar .nav-link:hover {
  background: var(--primary-light);
  color: var(--white);
}

.header {
  position: sticky;
  top: 0;
  right: 0;
  left: 0;
  height: 4rem;
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  z-index: 10;
}

.header .toggle-btn {
  background: none;
  border: none;
  color: var(--primary);
  font-size: 1.5rem;
  cursor: pointer;
}

.content {
  margin-right: var(--sidebar-width);
  padding: 2rem;
  transition: margin-right var(--transition);
}

.sidebar.collapsed ~ .content {
  margin-right: var(--sidebar-width-collapsed);
}

@media (max-width: 992px) {
  .sidebar {
    width: var(--sidebar-width-collapsed);
  }
  .content {
    margin-right: var(--sidebar-width-collapsed);
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .sidebar {
    right: -100vw;
    width: var(--sidebar-width);
    transition: right var(--transition);
  }
  .sidebar.open {
    right: 0;
  }
  .content {
    margin-right: 0;
    padding: 1rem;
  }
  .header {
    padding: 0 1rem;
  }
}

/* Forms */
input, select, textarea {
  font-family: inherit;
  font-size: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 1rem;
  background: var(--white);
  color: var(--text);
  transition: border-color var(--transition);
}

input:focus, select:focus, textarea:focus {
  border-color: var(--primary);
  outline: none;
}

button, .btn {
  background: var(--primary);
  color: var(--white);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background var(--transition), transform var(--transition);
}

button:hover, .btn:hover {
  background: var(--primary-light);
  transform: translateY(-2px);
}

/* ==================== */
/* LOGIN PAGE STYLES */
/* ==================== */

.login-body {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Cairo', sans-serif;
}

.login-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
}

.login-background {
  background: linear-gradient(135deg,
    rgba(35, 64, 160, 0.9) 0%,
    rgba(62, 94, 215, 0.8) 50%,
    rgba(118, 75, 162, 0.9) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.login-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(255,255,255,0.05) 0%, transparent 50%);
  pointer-events: none;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem 2.5rem;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 450px;
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.6s ease-out;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.logo-container {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 10px 30px rgba(35, 64, 160, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.logo-container i {
  font-size: 2.5rem;
  color: white;
}

.login-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text);
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  color: var(--text-muted);
  font-size: 1rem;
  margin-bottom: 0;
  font-weight: 400;
}

.login-form-container {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: var(--text);
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.label-icon {
  margin-left: 0.5rem;
  color: var(--primary);
  font-size: 1.1rem;
}

.input-wrapper {
  position: relative;
}

.modern-input {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 1rem;
  background: #fafbfc;
  transition: all 0.3s ease;
  font-family: inherit;
  direction: ltr;
}

.modern-input:focus {
  outline: none;
  border-color: var(--primary);
  background: white;
  box-shadow: 0 0 0 3px rgba(35, 64, 160, 0.1);
}

.modern-input:focus + .input-focus-border {
  transform: scaleX(1);
}

.input-focus-border {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  transform: scaleX(0);
  transition: transform 0.3s ease;
  border-radius: 0 0 12px 12px;
}

.field-validation-error {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: block;
}

.validation-summary {
  border-radius: 12px;
  border: none;
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #dc3545;
  animation: shake 0.5s ease-in-out;
}

.validation-summary-valid {
  display: none !important;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.remember-me-group {
  margin-bottom: 2rem;
}

.custom-checkbox {
  display: flex;
  align-items: center;
}

.checkbox-input {
  display: none;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.95rem;
  color: var(--text);
  user-select: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  margin-left: 0.75rem;
  position: relative;
  transition: all 0.3s ease;
  background: white;
}

.checkbox-input:checked + .checkbox-label .checkbox-custom {
  background: var(--primary);
  border-color: var(--primary);
}

.checkbox-input:checked + .checkbox-label .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.submit-group {
  margin-bottom: 0;
}

.btn-login {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  box-shadow: 0 8px 25px rgba(35, 64, 160, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-login::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s ease;
}

.btn-login:hover::before {
  left: 100%;
}

.btn-login:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(35, 64, 160, 0.4);
}

.btn-login:active {
  transform: translateY(0);
}

.btn-icon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.btn-login:hover .btn-icon {
  transform: translateX(-3px);
}

.login-footer {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid #e1e5e9;
}

.footer-text {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.footer-text i {
  color: #28a745;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-card {
    margin: 1rem;
    padding: 2rem 1.5rem;
    max-width: none;
  }

  .login-title {
    font-size: 1.75rem;
  }

  .logo-container {
    width: 70px;
    height: 70px;
  }

  .logo-container i {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 1rem 0.5rem;
  }

  .login-card {
    padding: 1.5rem 1rem;
  }

  .login-title {
    font-size: 1.5rem;
  }
}

/* Additional Interactive Effects */
.modern-input:invalid {
  border-color: #dc3545;
  background: rgba(220, 53, 69, 0.05);
}

.modern-input:valid {
  border-color: #28a745;
}

.form-group {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state for button */
.btn-login.loading {
  pointer-events: none;
  opacity: 0.7;
}

.btn-login.loading .btn-text::after {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Focus states */
.modern-input:focus {
  transform: translateY(-1px);
}

.checkbox-custom:hover {
  border-color: var(--primary);
  transform: scale(1.05);
}

/* Improved error styling */
.field-validation-error:not(:empty) {
  display: block;
  background: rgba(220, 53, 69, 0.1);
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  border-left: 3px solid #dc3545;
  margin-top: 0.5rem;
  animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success state */
.modern-input.success {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.05);
}

.modern-input.success + .input-focus-border {
  background: linear-gradient(90deg, #28a745, #20c997);
}

/* Floating label effect */
.form-label.floating {
  transform: translateY(-8px) scale(0.9);
  color: var(--primary);
}

/* Ripple effect */
.btn-login {
  position: relative;
  overflow: hidden;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Input wrapper focused state */
.input-wrapper.focused .modern-input {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(35, 64, 160, 0.1);
}

/* Error state */
.modern-input.error {
  border-color: #dc3545;
  background: rgba(220, 53, 69, 0.05);
  animation: shake 0.3s ease-in-out;
}

/* Smooth transitions for all interactive elements */
.modern-input,
.checkbox-custom,
.btn-login,
.form-label {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced focus indicators for accessibility */
.modern-input:focus-visible,
.checkbox-input:focus-visible + .checkbox-label .checkbox-custom,
.btn-login:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}