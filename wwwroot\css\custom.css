:root {
  --primary: #2340a0;
  --primary-light: #3e5ed7;
  --background: #f6f8fa;
  --white: #fff;
  --text: #222;
  --text-muted: #888;
  --radius: 1rem;
  --transition: 0.3s;
  --sidebar-width: 260px;
  --sidebar-width-collapsed: 64px;
}

html {
  direction: rtl;
}

body {
  margin: 0;
  font-family: 'Cairo', system-ui, sans-serif;
  background: var(--background);
  color: var(--text);
  min-height: 100vh;
}

.sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--primary);
  color: var(--white);
  display: flex;
  flex-direction: column;
  transition: width var(--transition);
  z-index: 100;
}

.sidebar.collapsed {
  width: var(--sidebar-width-collapsed);
}

.sidebar .brand {
  font-size: 1.3rem;
  font-weight: bold;
  padding: 1.5rem 1rem;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sidebar nav {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
}

.sidebar .nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--white);
  text-decoration: none;
  padding: 0.75rem 1rem;
  border-radius: var(--radius);
  transition: background var(--transition), color var(--transition);
}

.sidebar .nav-link.active,
.sidebar .nav-link:hover {
  background: var(--primary-light);
  color: var(--white);
}

.header {
  position: sticky;
  top: 0;
  right: 0;
  left: 0;
  height: 4rem;
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  z-index: 10;
}

.header .toggle-btn {
  background: none;
  border: none;
  color: var(--primary);
  font-size: 1.5rem;
  cursor: pointer;
}

.content {
  margin-right: var(--sidebar-width);
  padding: 2rem;
  transition: margin-right var(--transition);
}

.sidebar.collapsed ~ .content {
  margin-right: var(--sidebar-width-collapsed);
}

@media (max-width: 992px) {
  .sidebar {
    width: var(--sidebar-width-collapsed);
  }
  .content {
    margin-right: var(--sidebar-width-collapsed);
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .sidebar {
    right: -100vw;
    width: var(--sidebar-width);
    transition: right var(--transition);
  }
  .sidebar.open {
    right: 0;
  }
  .content {
    margin-right: 0;
    padding: 1rem;
  }
  .header {
    padding: 0 1rem;
  }
}

/* Forms */
input, select, textarea {
  font-family: inherit;
  font-size: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 1rem;
  background: var(--white);
  color: var(--text);
  transition: border-color var(--transition);
}

input:focus, select:focus, textarea:focus {
  border-color: var(--primary);
  outline: none;
}

button, .btn {
  background: var(--primary);
  color: var(--white);
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background var(--transition), transform var(--transition);
}

button:hover, .btn:hover {
  background: var(--primary-light);
  transform: translateY(-2px);
}

/* ==================== */
/* LOGIN PAGE STYLES */
/* ==================== */

/* Simple login page styles using Bootstrap - minimal custom CSS */

/* All login styles now handled by Bootstrap classes in the view */

/* Login styles removed - using Bootstrap only */

/* Logout button styling in sidebar */
.sidebar .nav-link form {
  margin: 0;
  padding: 0;
}

.sidebar .nav-link form button {
  color: inherit;
  text-decoration: none;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  width: 100%;
  text-align: right;
  background: none;
  border: none;
  font-size: inherit;
  font-family: inherit;
}

.sidebar .nav-link form button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: inherit;
}

.sidebar .nav-link form button i {
  margin-left: 0.5rem;
  width: 20px;
  text-align: center;
}

/* End of login styles cleanup */

/* ==================== */
/* ENHANCED TABLE STYLES */
/* ==================== */

/* Statistics Cards */
.border-left-primary {
  border-left: 0.25rem solid var(--primary) !important;
}

.border-left-success {
  border-left: 0.25rem solid #28a745 !important;
}

.border-left-info {
  border-left: 0.25rem solid #17a2b8 !important;
}

.border-left-warning {
  border-left: 0.25rem solid #ffc107 !important;
}

.text-gray-800 {
  color: #5a5c69 !important;
}

.text-gray-300 {
  color: #dddfeb !important;
}

/* Enhanced Table Styling */
.table-responsive {
  position: relative;
  border-radius: 0.375rem;
  overflow: hidden;
}

.table thead th {
  border-bottom: 2px solid var(--primary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

.table tbody tr {
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background-color: rgba(35, 64, 160, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Badge Enhancements */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
}

/* Button Group Enhancements */
.btn-group .btn {
  border-radius: 0.375rem;
  margin: 0 0.125rem;
}

.btn-outline-info:hover {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-danger:hover {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

/* DataTable Custom Styling */
.dataTables_wrapper {
  padding: 0;
}

.dataTables_filter {
  margin-bottom: 1rem;
}

.dataTables_filter input {
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  padding: 0.5rem 0.75rem;
  margin-left: 0.5rem;
}

.dataTables_length select {
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  padding: 0.375rem 0.75rem;
  margin: 0 0.5rem;
}

.dataTables_info {
  color: #6c757d;
  font-size: 0.875rem;
}

.dataTables_paginate .paginate_button {
  padding: 0.375rem 0.75rem;
  margin: 0 0.125rem;
  border-radius: 0.375rem;
  border: 1px solid #dee2e6;
  color: var(--primary);
  text-decoration: none;
}

.dataTables_paginate .paginate_button:hover {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.dataTables_paginate .paginate_button.current {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  border-radius: 0.375rem;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.875rem;
  }

  .btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .badge {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
  }

  .card-header .btn-group {
    flex-direction: column;
    width: 100%;
  }

  .card-header .btn-group .btn {
    margin: 0.125rem 0;
  }

  .statistics-cards .col-xl-3 {
    margin-bottom: 1rem;
  }
}

@media (max-width: 576px) {
  .table {
    font-size: 0.75rem;
  }

  .card-header {
    padding: 1rem;
  }

  .card-header h6 {
    font-size: 0.875rem;
  }

  .btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }
}

/* Table Row Selection */
.table tbody tr.selected {
  background-color: rgba(35, 64, 160, 0.15);
}

/* DataTable Buttons */
.dt-buttons {
  margin-bottom: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.dt-button {
  border-radius: 0.375rem;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border: none;
  transition: all 0.3s ease;
}

.dt-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Text Truncation */
.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Phone Link Styling */
a[href^="tel:"] {
  color: #28a745;
  text-decoration: none;
}

a[href^="tel:"]:hover {
  color: #1e7e34;
  text-decoration: underline;
}

/* Fixed Header Enhancement */
.fixedHeader-floating {
  background-color: var(--white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ==================== */
/* EDIT FORM STYLES */
/* ==================== */

/* Form Section Headers */
.form-section-header {
  border-left: 4px solid var(--primary);
  padding-left: 1rem;
  margin-bottom: 1.5rem;
}

/* Enhanced Form Controls */
.form-control-lg:focus,
.form-select-lg:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(35, 64, 160, 0.25);
  transform: translateY(-1px);
}

/* Form Validation States */
.form-control.is-valid,
.form-select.is-valid {
  border-color: #28a745;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.88 1.88 3.75-3.75.94.94-4.69 4.69z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid,
.form-select.is-invalid {
  border-color: #dc3545;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4L5.8 7'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Form Switch Enhancement */
.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.form-check-input:focus {
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(35, 64, 160, 0.25);
}

/* Card Header Enhancement */
.card-header.bg-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-light)) !important;
}

/* Form Labels */
.form-label.fw-bold {
  color: #495057;
  margin-bottom: 0.75rem;
}

/* Section Dividers */
.border-bottom {
  border-bottom: 2px solid #e9ecef !important;
}

/* Button Enhancements */
.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.btn-lg:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Form Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeInUp 0.6s ease-out;
}

/* Responsive Form Adjustments */
@media (max-width: 768px) {
  .form-control-lg,
  .form-select-lg {
    font-size: 1rem;
    padding: 0.75rem 1rem;
  }

  .btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }

  .card-body {
    padding: 1.5rem !important;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .d-flex.justify-content-between > div {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .col-md-6,
  .col-md-4 {
    margin-bottom: 1rem;
  }

  .form-label {
    font-size: 0.9rem;
  }

  .card-header h5 {
    font-size: 1rem;
  }
}

/* Loading State */
.btn.loading {
  pointer-events: none;
  opacity: 0.7;
}

.btn.loading::after {
  content: '';
  display: inline-block;
  width: 1rem;
  height: 1rem;
  margin-left: 0.5rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Textarea Enhancement */
textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

textarea.form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(35, 64, 160, 0.25);
}