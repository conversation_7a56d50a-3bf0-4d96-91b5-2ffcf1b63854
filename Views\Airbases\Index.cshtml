@model IEnumerable<TajneedApp.Models.Airbase>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table table-bordered table-hover table-striped table-sm">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.AirbaseName)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.AirbaseName)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.AirbaseId">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.AirbaseId">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.AirbaseId">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
