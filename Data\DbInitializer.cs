using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using TajneedApp.Models;

namespace TajneedApp.Data
{
    public static class DbInitializer
    {
        public static async Task InitializeAsync(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            ILogger<ApplicationDbContext> logger)
        {
            try
            {
                // Ensure database is created and all migrations are applied
                if (context.Database.GetPendingMigrations().Any())
                {
                    logger.LogInformation("Applying pending migrations...");
                    await context.Database.MigrateAsync();
                }

                // Seed Ranks if they don't exist
                if (!context.Ranks.Any())
                {
                    logger.LogInformation("Seeding Ranks data...");
                    var ranks = new Rank[]
                    {
                        new Rank { RankName = "جندي" },
                        new Rank { RankName = "نائب عريف" },
                        new Rank { RankName = "عريف" },
                        new Rank { RankName = "رقيب" },
                        new Rank { RankName = "رقيب أول" },
                        new Rank { RankName = "وكيل" },
                        new Rank { RankName = "وكيل أول" },
                        new Rank { RankName = "ضابط مرشح" },
                        new Rank { RankName = "ملازم ثاني" },
                        new Rank { RankName = "ملازم أول" },
                        new Rank { RankName = "نقيب" },
                        new Rank { RankName = "رائد" },
                        new Rank { RankName = "مقدم" },
                        new Rank { RankName = "عقيد" },
                        new Rank { RankName = "عميد" },
                        new Rank { RankName = "لواء" },
                        new Rank { RankName = "فريق" },
                        new Rank { RankName = "مدني درجة 16" },
                        new Rank { RankName = "مدني درجة 15" },
                        new Rank { RankName = "مدني درجة 14" },
                        new Rank { RankName = "مدني درجة 13" },
                        new Rank { RankName = "مدني درجة 12" },
                        new Rank { RankName = "مدني درجة 11" },
                        new Rank { RankName = "مدني درجة 10" },
                        new Rank { RankName = "مدني درجة 9" },
                        new Rank { RankName = "ضابط مدني د8" },
                        new Rank { RankName = "ضابط مدني د9" },
                        new Rank { RankName = "ضابط مدني د7" },
                        new Rank { RankName = "ضابط مدني د6" },
                        new Rank { RankName = "ضابط مدني د5" },
                        new Rank { RankName = "ضابط مدني د4" },
                        new Rank { RankName = "ضابط مدني د3" },
                        new Rank { RankName = "ضابط مدني د2" },
                        new Rank { RankName = "ضابط مدني د1" },
                    };

                    context.Ranks.AddRange(ranks);
                    await context.SaveChangesAsync();
                    logger.LogInformation($"Successfully seeded {ranks.Length} ranks.");
                }

                // Seed Admin Role if it doesn't exist
                if (!await roleManager.RoleExistsAsync("Admin"))
                {
                    logger.LogInformation("Creating Admin role...");
                    await roleManager.CreateAsync(new IdentityRole("Admin"));
                    logger.LogInformation("Admin role created successfully.");
                }

                // Seed Admin User if it doesn't exist
                var adminEmail = "<EMAIL>";
                var adminUser = await userManager.FindByEmailAsync(adminEmail);

                if (adminUser == null)
                {
                    logger.LogInformation("Creating admin user...");
                    // Get the highest rank (عميد) for the admin
                    var adminRank = await context.Ranks.FirstOrDefaultAsync(r => r.RankName == "عميد")
                        ?? throw new InvalidOperationException("Required rank 'عميد' not found in database");

                    adminUser = new ApplicationUser
                    {
                        UserName = adminEmail,
                        Email = adminEmail,
                        ServiceNumber = "ADMIN001",
                        FullName = "مدير النظام",
                        RankId = adminRank.RankId,
                        EmailConfirmed = true,
                        IsActive = true
                    };

                    var result = await userManager.CreateAsync(adminUser, "Admin@123456");
                    if (result.Succeeded)
                    {
                        logger.LogInformation("Admin user created successfully.");
                        await userManager.AddToRoleAsync(adminUser, "Admin");
                        logger.LogInformation("Added admin user to Admin role.");
                    }
                    else
                    {
                        var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                        logger.LogError($"Failed to create admin user. Errors: {errors}");
                        throw new InvalidOperationException($"Failed to create admin user: {errors}");
                    }
                }
                else
                {
                    // Update existing admin user to ensure IsActive is true
                    if (!adminUser.IsActive)
                    {
                        logger.LogInformation("Updating admin user to set IsActive = true");
                        adminUser.IsActive = true;
                        var updateResult = await userManager.UpdateAsync(adminUser);
                        if (updateResult.Succeeded)
                        {
                            logger.LogInformation("Admin user updated successfully.");
                        }
                        else
                        {
                            var errors = string.Join(", ", updateResult.Errors.Select(e => e.Description));
                            logger.LogError($"Failed to update admin user. Errors: {errors}");
                        }
                    }
                }

                // Add role claims
                var adminRole = await roleManager.FindByNameAsync("Admin");
                if (adminRole != null)
                {
                    var existingClaims = await roleManager.GetClaimsAsync(adminRole);
                    if (!existingClaims.Any(c => c.Type == "Permission"))
                    {
                        await roleManager.AddClaimAsync(adminRole, new Claim("Permission", "ManageUsers"));
                        await roleManager.AddClaimAsync(adminRole, new Claim("Permission", "ManageRoles"));
                        logger.LogInformation("Added permission claims to Admin role");
                    }
                }

                // Create test user with officer rank
                var testOfficerEmail = "<EMAIL>";
                var testOfficer = await userManager.FindByEmailAsync(testOfficerEmail);

                if (testOfficer == null)
                {
                    // Get officer rank (نقيب)
                    var officerRank = await context.Ranks.FirstOrDefaultAsync(r => r.RankName == "نقيب")
                        ?? throw new InvalidOperationException("Required rank 'نقيب' not found in database");

                    testOfficer = new ApplicationUser
                    {
                        UserName = testOfficerEmail,
                        Email = testOfficerEmail,
                        ServiceNumber = "OFF123",
                        FullName = "ضابط اختبار",
                        RankId = officerRank.RankId,
                        EmailConfirmed = true,
                        IsActive = true
                    };

                    var result = await userManager.CreateAsync(testOfficer, "Officer@123456");
                    if (result.Succeeded)
                    {
                        logger.LogInformation("Test officer user created successfully");

                        // Add custom claims for the test officer
                        await userManager.AddClaimsAsync(testOfficer, new[]
                        {
                            new Claim("Department", "Personnel"),
                            new Claim("Office", "MainOffice"),
                            new Claim("JoinDate", DateTime.UtcNow.ToString("yyyy-MM-dd"))
                        });
                        logger.LogInformation("Added claims to test officer user");
                    }
                    else
                    {
                        var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                        logger.LogError($"Failed to create test officer user. Errors: {errors}");
                    }
                }

                // Create test enlisted user
                var testEnlistedEmail = "<EMAIL>";
                var testEnlisted = await userManager.FindByEmailAsync(testEnlistedEmail);

                if (testEnlisted == null)
                {
                    // Get enlisted rank (رقيب)
                    var enlistedRank = await context.Ranks.FirstOrDefaultAsync(r => r.RankName == "رقيب")
                        ?? throw new InvalidOperationException("Required rank 'رقيب' not found in database");

                    testEnlisted = new ApplicationUser
                    {
                        UserName = testEnlistedEmail,
                        Email = testEnlistedEmail,
                        ServiceNumber = "ENL456",
                        FullName = "رقيب اختبار",
                        RankId = enlistedRank.RankId,
                        EmailConfirmed = true,
                        IsActive = true
                    };

                    var result = await userManager.CreateAsync(testEnlisted, "Enlisted@123456");
                    if (result.Succeeded)
                    {
                        logger.LogInformation("Test enlisted user created successfully");

                        // Add custom claims for the enlisted user
                        await userManager.AddClaimsAsync(testEnlisted, new[]
                        {
                            new Claim("Department", "Operations"),
                            new Claim("Section", "Alpha"),
                            new Claim("JoinDate", DateTime.UtcNow.ToString("yyyy-MM-dd"))
                        });
                        logger.LogInformation("Added claims to test enlisted user");
                    }
                    else
                    {
                        var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                        logger.LogError($"Failed to create test enlisted user. Errors: {errors}");
                    }
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while initializing the database.");
                throw;
            }
        }
    }
}
