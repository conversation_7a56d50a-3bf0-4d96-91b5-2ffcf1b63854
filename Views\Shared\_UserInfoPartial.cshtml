@using Microsoft.AspNetCore.Identity
@using TajneedApp.Models
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

@{
    var user = await UserManager.GetUserAsync(User);
    var userName = user?.UserName;
    var isAdmin = User.IsInRole("Admin");
}

@if (SignInManager.IsSignedIn(User))
{
 

<div class="user-info-panel-compact">
    <div class="user-avatar-compact">
        <i class="ri-user-fill"></i>
    </div>
    <div class="user-details-compact">
        <span class="user-name-compact">@User.Identity.Name</span>
        @if (User.IsInRole("Admin"))
        {
            <span class="user-role-compact">مسؤول</span>
        }
        else
        {
            <span class="user-role-compact">مستخدم عادي</span>
        }
    </div>
    <div class="user-actions-compact">
        <a class="user-action-link-compact" asp-controller="Account" asp-action="Settings" title="الإعدادات">
            <i class="ri-settings-3-line"></i>
        </a>
        <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Page("/", new { area = "" })" method="post">
            <button type="submit" class="user-action-link-compact" title="تسجيل الخروج">
                <i class="ri-logout-box-r-line"></i>
            </button>
        </form>
    </div>
</div>
}