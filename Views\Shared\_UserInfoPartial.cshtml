@using System.Security.Claims



@{
    var fullName = User.FindFirstValue("FullName") ?? string.Empty;
    var rank = User.FindFirstValue("Rank") ?? string.Empty;
    var serviceNumber = User.FindFirstValue("ServiceNumber") ?? string.Empty;
    var isActive = User.FindFirstValue("IsActive") == "True";


}

@if (isAuthenticated)
{
<div class="card shadow-sm mb-4" dir="rtl">
    <div class="card-body">
        @if (TempData["Success"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>@TempData["Success"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        }
        <div class="d-flex align-items-center mb-3 flex-wrap">
            <div class="avatar avatar-lg bg-primary bg-opacity-10 text-primary rounded-circle d-flex align-items-center justify-content-center ms-4">
                <i class="fa-solid fa-person-military-rifle"></i>
            </div>
            <div class="flex-grow-1">
                <div class="d-flex mb-1">
                    <h5 class="fw-bolder mb-0">الاسم:</h5>
                    <span class="mx-2">@fullName</span>
                </div>
                <div class="d-flex mb-1">
                    <h5 class="fw-bolder mb-0">الرتبة:</h5>
                    <span class="mx-2">@rank</span>
                </div>
                <div class="d-flex">
                    <div class="ms-4 mx-2">
                        <strong>الرقم العسكري:</strong> @serviceNumber
                    </div>
                    <div>
                        <strong class="mx-2">الحالة:</strong>
                        <span class="status-indicator @((isActive ? "status-active" : "status-inactive"))"></span>
                        @(isActive ? "نشط" : "غير نشط")
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex justify-content-end flex-wrap gap-2">
            <a class="btn btn-primary btn-sm ms-2" href="#">
                <i class="fas fa-user ms-2"></i>الملف الشخصي
            </a>
            <a class="btn btn-secondary btn-sm ms-2" href="#">
                <i class="fas fa-cog ms-2"></i>الإعدادات
            </a>
            <form method="post" asp-controller="Account" asp-action="Logout" class="d-inline">
                @Html.AntiForgeryToken()
                <button type="submit" class="btn btn-danger btn-sm">
                    <i class="fas fa-sign-out-alt ms-2"></i>تسجيل الخروج
                </button>
            </form>
        </div>

    
        <div>
        <div>
       
    
    </div>
    </div>
    </div>
</div>
}


<style>
    .card {
        border-radius: 12px;
    }
    .avatar-lg {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    .status-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin-left: 6px; /* RTL: left instead of right */
    }
    .status-active {
        background-color: #28a745;
    }
    .status-inactive {
        background-color: #dc3545;
    }
</style>
