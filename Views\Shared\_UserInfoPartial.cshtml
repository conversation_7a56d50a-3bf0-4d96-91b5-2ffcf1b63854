@using System.Security.Claims
@using TajneedApp.Controllers

@{
    var fullName = User.FindFirstValue("FullName") ?? string.Empty;
    var rank = User.FindFirstValue("Rank") ?? string.Empty;
    var serviceNumber = User.FindFirstValue("ServiceNumber") ?? string.Empty;
    var isActive = User.FindFirstValue("IsActive") == "True";
    var position = User.FindFirstValue("Position") ?? string.Empty;
    var isCommitteeMember = User.FindFirstValue("IsCommitteeMember") == "True";
}

@if (serviceNumber != null)
{
<div class="card shadow-sm mb-4 user-profile-card" dir="rtl">
    <div class="card-body">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        }
        <div class="d-flex align-items-center mb-3 flex-wrap">
            <div class="avatar avatar-lg bg-primary bg-opacity-10 text-primary rounded-circle d-flex align-items-center justify-content-center ms-4">
                <i class="fa-solid fa-person-military-rifle"></i>
            </div>
            <div class="flex-grow-1">
                <div class="user-info-item">
                    <span class="info-label">الاسم:</span>
                    <span class="info-value">@fullName</span>
                </div>
                <div class="user-info-item">
                    <span class="info-label">الرتبة:</span>
                    <span class="info-value">@rank</span>
                    @if (!string.IsNullOrEmpty(position))
                    {
                        <span class="badge bg-info ms-2">@position</span>
                    }
                    @if (isCommitteeMember)
                    {
                        <span class="badge bg-purple ms-2">عضو لجنة</span>
                    }
                </div>
                <div class="user-info-row">
                    <div class="user-info-item compact">
                        <span class="info-label">الرقم العسكري:</span>
                        <span class="info-value">@serviceNumber</span>
                    </div>
                    <div class="user-info-item compact">
                        <span class="info-label">الحالة:</span>
                        <span class="info-value">
                            <span class="status-indicator @((isActive ? "status-active" : "status-inactive"))"></span>
                            @(isActive ? "نشط" : "غير نشط")
                        </span>
                    </div>
                </div>
            </div>
        </div>
        @* <div class="d-flex justify-content-end flex-wrap gap-2 action-buttons">
            <a class="btn btn-primary btn-sm ms-2" href="#">
                <i class="fas fa-user me-1"></i>الملف الشخصي
            </a>
            <a class="btn btn-outline-secondary btn-sm ms-2" href="#">
                <i class="fas fa-cog me-1"></i>الإعدادات
            </a>
            <a asp-controller="Login" asp-action="Logout" class="btn btn-outline-danger btn-sm">
                <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
            </a>
        </div> *@
        @if (!isActive)
        {
        <div class="alert alert-warning mt-3 text-center account-alert" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>الحساب غير نشط - بعض الميزات قد لا تكون متاحة
        </div>
        }
    </div>
</div>
}

<style>
    .user-profile-card {
        border-radius: 12px;
        border: 1px solid rgba(0, 0, 0, 0.08);
    }
    
    .avatar-lg {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        transition: transform 0.3s ease;
    }
    
    .avatar-lg:hover {
        transform: scale(1.05);
    }
    
    .user-info-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .user-info-row {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .user-info-item.compact {
        margin-bottom: 0;
    }
    
    .info-label {
        font-weight: 600;
        color: #555;
        min-width: 70px;
    }
    
    .info-value {
        color: #333;
        margin-right: 8px;
    }
    
    .status-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin-left: 6px;
        vertical-align: middle;
    }
    
    .status-active {
        background-color: #28a745;
        box-shadow: 0 0 8px rgba(40, 167, 69, 0.4);
    }
    
    .status-inactive {
        background-color: #dc3545;
        box-shadow: 0 0 8px rgba(220, 53, 69, 0.4);
    }
    
    .badge {
        font-weight: 500;
        font-size: 0.75rem;
        padding: 0.35em 0.65em;
    }
    
    .bg-purple {
        background-color: #6f42c1;
    }
    
    .action-buttons .btn {
        transition: all 0.2s ease;
        border-radius: 6px;
        padding: 0.375rem 0.75rem;
    }
    
    .action-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .account-alert {
        border-radius: 8px;
        padding: 0.75rem 1rem;
    }
    
    @@media (max-width: 576px) {
        .user-info-row {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .action-buttons {
            justify-content: center !important;
        }
    }
</style>