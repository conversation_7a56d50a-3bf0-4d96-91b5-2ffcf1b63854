using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace TajneedApp.Models;

public partial class CandidateCommitteeEvaluation
{
    [Key]
    public int EvaluationId { get; set; }

    [Display(Name = "معرف المرشح")]
    [Required(ErrorMessage = "حقل معرف المرشح مطلوب.")]
    public int CandidateId { get; set; }

    [Display(Name = "معرف المعيار")]
    [Required(ErrorMessage = "حقل معرف المعيار مطلوب.")]
    public int CriteriaId { get; set; }

    [Display(Name = "الدرجة")]
    [Required(ErrorMessage = "حقل الدرجة مطلوب.")]
    [Range(0, 100, ErrorMessage = "الدرجة يجب أن تكون بين 0 و 100")]
    public decimal Score { get; set; }

    [Display(Name = "المقيم")]
    [Required(ErrorMessage = "حقل المقيم مطلوب.")]
    public string? EvaluatedBy { get; set; }

    [Display(Name = "تاريخ التقييم")]
    [Required(ErrorMessage = "حقل تاريخ التقييم مطلوب.")]
    [DataType(DataType.DateTime)]
    public DateTime EvaluationDate { get; set; } = DateTime.UtcNow;

    [Display(Name = "ملاحظات")]
    [StringLength(1000, ErrorMessage = "الملاحظات يجب أن لا تتجاوز 1000 حرف.")]
    public string? Comments { get; set; }

    [Display(Name = "تاريخ الإنشاء")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    [Display(Name = "المستخدم المنشئ")]
    public string? CreatedBy { get; set; }

    [Display(Name = "تاريخ آخر تعديل")]
    public DateTime? LastModifiedDate { get; set; }

    [Display(Name = "المستخدم المعدل")]
    public string? LastModifiedBy { get; set; }

    // Navigation Properties
    [ValidateNever]
    [Display(Name = "المرشح")]
    public virtual Candidate? Candidate { get; set; }

    [ValidateNever]
    [Display(Name = "المعيار")]
    public virtual CommitteeEvaluationCriteria? Criteria { get; set; }
}
