@model TajneedApp.Models.EvaluationComponent

@{
    ViewData["Title"] = "إضافة مكون تقييم جديد";
}

<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h3 class="m-0 font-weight-bold">إضافة مكون تقييم جديد</h3>
        </div>
        <div class="card-body">
            <form asp-action="Create">
                <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="ComponentName" class="form-label"></label>
                            <input asp-for="ComponentName" class="form-control" placeholder="مثال: الامتحان المكتوب" />
                            <span asp-validation-for="ComponentName" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="EvaluationPathId" class="form-label"></label>
                            <select asp-for="EvaluationPathId" class="form-control" asp-items="ViewBag.EvaluationPathId">
                                <option value="">اختر مسار التقييم</option>
                            </select>
                            <span asp-validation-for="EvaluationPathId" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="ComponentDescription" class="form-label"></label>
                    <textarea asp-for="ComponentDescription" class="form-control" rows="3" placeholder="وصف تفصيلي للمكون"></textarea>
                    <span asp-validation-for="ComponentDescription" class="text-danger"></span>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label asp-for="ComponentType" class="form-label"></label>
                            <select asp-for="ComponentType" class="form-control" asp-items="ViewBag.ComponentType" id="componentType">
                                <option value="">اختر نوع المكون</option>
                            </select>
                            <span asp-validation-for="ComponentType" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label asp-for="EvaluationType" class="form-label"></label>
                            <select asp-for="EvaluationType" class="form-control" asp-items="ViewBag.EvaluationType">
                                <option value="">اختر نوع التقييم</option>
                            </select>
                            <span asp-validation-for="EvaluationType" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label asp-for="WeightPercentage" class="form-label"></label>
                            <div class="input-group">
                                <input asp-for="WeightPercentage" class="form-control" type="number" step="0.01" min="0.01" max="100" />
                                <span class="input-group-text">%</span>
                            </div>
                            <span asp-validation-for="WeightPercentage" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row" id="scoreFields">
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label asp-for="MaxScore" class="form-label"></label>
                            <input asp-for="MaxScore" class="form-control" type="number" min="1" />
                            <span asp-validation-for="MaxScore" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label asp-for="PassingScore" class="form-label"></label>
                            <input asp-for="PassingScore" class="form-control" type="number" min="0" />
                            <span asp-validation-for="PassingScore" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label asp-for="DisplayOrder" class="form-label"></label>
                            <input asp-for="DisplayOrder" class="form-control" type="number" min="1" />
                            <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <div class="form-check">
                                <input asp-for="IsRequired" class="form-check-input" type="checkbox" checked />
                                <label asp-for="IsRequired" class="form-check-label">
                                    مكون إجباري
                                </label>
                            </div>
                            <small class="form-text text-muted">المكونات الإجبارية يجب اجتيازها للنجاح</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" type="checkbox" checked />
                                <label asp-for="IsActive" class="form-check-label">
                                    تفعيل المكون
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info" id="committeeEvaluationNote" style="display: none;">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> بعد إنشاء مكون تقييم اللجنة، ستتمكن من إضافة معايير التقييم التفصيلية.
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> إنشاء المكون
                    </button>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للقائمة
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Show/hide score fields based on evaluation type
            function toggleScoreFields() {
                var evaluationType = $('#EvaluationType').val();
                if (evaluationType === '1') { // Mark
                    $('#scoreFields').show();
                } else {
                    $('#scoreFields').hide();
                }
            }

            // Show/hide committee evaluation note
            function toggleCommitteeNote() {
                var componentType = $('#componentType').val();
                if (componentType === '3') { // CommitteeEvaluation
                    $('#committeeEvaluationNote').show();
                } else {
                    $('#committeeEvaluationNote').hide();
                }
            }

            $('#EvaluationType').change(toggleScoreFields);
            $('#componentType').change(toggleCommitteeNote);
            
            // Initial state
            toggleScoreFields();
            toggleCommitteeNote();
        });
    </script>
}
