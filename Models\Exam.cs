﻿
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace TajneedApp.Models;

public enum EvaluationType
{
    [Display(Name = "درجة")]
    Mark = 1,
    [Display(Name = "ناجح / راسب")]
    PassFail = 2,
    [Display(Name = "لائق /غير لائق")]
    FitInfit = 3,
}

public partial class Exam
{
    [Key]
    public int ExamId { get; set; }

    [Display(Name = "اسم الامتحان")]
    [Required(ErrorMessage = "الرجاء إدخال اسم الامتحان")]
    public string? ExamName { get; set; }

    [Display(Name = " الفئة")]
    [Required(ErrorMessage = "الرجاء اختيار الفئة")]
    public int? CategoryId { get; set; }

    [Display(Name = "نوع التقييم")]
    [Required(ErrorMessage = "الرجاء اختيار نوع التقييم")]
    public EvaluationType EvaluationType { get; set; }

    [NotMapped]
    public string EvaluationTypeValue 
    { 
        get => EvaluationType.ToString();
        set => EvaluationType = Enum.Parse<EvaluationType>(value);
    }

    [Display(Name = "الدرجة المطلوبة")]
    public int? PassingScore { get; set; }

    [Display(Name = "الدرجة القصوى")]
    public int? MaxScore { get; set; }

    [ValidateNever]
    public virtual Category? Category { get; set; }
}