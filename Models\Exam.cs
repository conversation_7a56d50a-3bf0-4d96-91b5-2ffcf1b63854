﻿
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace TajneedApp.Models;

public enum EvaluationType
{
    [Display(Name = "درجة")]
    Mark = 1,
    [Display(Name = "ناجح / راسب")]
    PassFail = 2,
    [Display(Name = "لائق /غير لائق")]
    FitInfit = 3,
}

public partial class Exam
{
    [Key]
    public int ExamId { get; set; }

    [Display(Name = "اسم الامتحان")]
    [Required(ErrorMessage = "الرجاء إدخال اسم الامتحان")]
    public string? ExamName { get; set; }

    [Display(Name = " الفئة")]
    [Required(ErrorMessage = "الرجاء اختيار الفئة")]
    public int? CategoryId { get; set; }

    [Display(Name = "نوع التقييم")]
    [Required(ErrorMessage = "الرجاء اختيار نوع التقييم")]
    public EvaluationType EvaluationType { get; set; }

    [NotMapped]
    public string EvaluationTypeValue
    {
        get => EvaluationType.ToString();
        set => EvaluationType = Enum.Parse<EvaluationType>(value);
    }

    [Display(Name = "الدرجة المطلوبة")]
    public int? PassingScore { get; set; }

    [Display(Name = "الدرجة القصوى")]
    public int? MaxScore { get; set; }

    [Display(Name = "نسبة الوزن (%)")]
    [Required(ErrorMessage = "حقل نسبة الوزن مطلوب.")]
    [Range(0.01, 100, ErrorMessage = "نسبة الوزن يجب أن تكون بين 0.01 و 100")]
    public decimal WeightPercentage { get; set; }

    [Display(Name = "ترتيب العرض")]
    public int DisplayOrder { get; set; } = 1;

    [Display(Name = "نشط")]
    public bool IsActive { get; set; } = true;

    [Display(Name = "تاريخ الإنشاء")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    [Display(Name = "المستخدم المنشئ")]
    public string? CreatedBy { get; set; }

    // Navigation Properties
    [ValidateNever]
    [Display(Name = " الفئة")]
    public virtual Category? Category { get; set; }

    [ValidateNever]
    public virtual ICollection<CommitteeEvaluationCriteria> CommitteeCriteria { get; set; } = new List<CommitteeEvaluationCriteria>();

    [ValidateNever]
    public virtual ICollection<CandidateExamResult> ExamResults { get; set; } = new List<CandidateExamResult>();
}