﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TajneedApp.Migrations
{
    /// <inheritdoc />
    public partial class FixCascadeDeleteConstraints : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CandidateExamResults_Exams_ExamId",
                table: "CandidateExamResults");

            migrationBuilder.AddForeignKey(
                name: "FK_CandidateExamResults_Exams_ExamId",
                table: "CandidateExamResults",
                column: "ExamId",
                principalTable: "Exams",
                principalColumn: "ExamId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CandidateExamResults_Exams_ExamId",
                table: "CandidateExamResults");

            migrationBuilder.AddForeignKey(
                name: "FK_CandidateExamResults_Exams_ExamId",
                table: "CandidateExamResults",
                column: "ExamId",
                principalTable: "Exams",
                principalColumn: "ExamId",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
