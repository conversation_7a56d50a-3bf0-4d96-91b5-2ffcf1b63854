﻿"restore":{"projectUniqueName":"D:\\TajneedApp\\TajneedApp.csproj","projectName":"TajneedApp","projectPath":"D:\\TajneedApp\\TajneedApp.csproj","outputPath":"D:\\TajneedApp\\obj\\","projectStyle":"PackageReference","fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net9.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.300"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore":{"target":"Package","version":"[9.0.5, )"},"Microsoft.AspNetCore.Identity.EntityFrameworkCore":{"target":"Package","version":"[9.0.5, )"},"Microsoft.AspNetCore.Identity.UI":{"target":"Package","version":"[9.0.5, )"},"Microsoft.EntityFrameworkCore.SqlServer":{"target":"Package","version":"[9.0.5, )"},"Microsoft.EntityFrameworkCore.SqlServer.Design":{"target":"Package","version":"[1.1.6, )"},"Microsoft.EntityFrameworkCore.Sqlite":{"target":"Package","version":"[9.0.5, )"},"Microsoft.EntityFrameworkCore.Tools":{"target":"Package","version":"[9.0.5, )"},"Microsoft.VisualStudio.Web.CodeGeneration.Design":{"target":"Package","version":"[9.0.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.AspNetCore.App":{"privateAssets":"none"},"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}