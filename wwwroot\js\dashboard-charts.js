// Set global Chart.js font configuration
Chart.defaults.font.family = 'Cairo';
Chart.defaults.color = '#333'; // Darker color for better readability

// Helper function to display "No data" message
function showNoDataMessage(canvasId, message = 'لا توجد بيانات لعرضها') {
    const canvas = document.getElementById(canvasId);
    if (canvas) {
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear previous content
        ctx.font = '16px Cairo';
        ctx.fillStyle = '#6c757d';
        ctx.textAlign = 'center';
        ctx.fillText(message, canvas.width / 2, canvas.height / 2);
    }
}

// Helper function to generate chart colors
function generateChartColors(count) {
    const baseColors = [
        'rgba(0, 123, 255, 0.6)', // Blue
        'rgba(255, 99, 132, 0.6)', // Red
        'rgba(54, 162, 235, 0.6)', // Sky Blue
        'rgba(255, 206, 86, 0.6)', // Yellow
        'rgba(75, 192, 192, 0.6)', // Green
        'rgba(153, 102, 255, 0.6)', // Purple
        'rgba(255, 159, 64, 0.6)'  // Orange
    ];
    const colors = [];
    for (let i = 0; i < count; i++) {
        colors.push(baseColors[i % baseColors.length]);
    }
    return colors;
}

function generateBorderColors(colors) {
    return colors.map(color => color.replace('0.6', '1')); // Make border fully opaque
}

$(document).ready(function () {
    // Airbase Chart
    // These variables are expected to be defined in the CSHTML view:
    // airbaseChartData.labels, airbaseChartData.data
    const airbaseCanvas = document.getElementById('airbaseChart');

    if (airbaseCanvas && typeof airbaseChartData !== 'undefined' && airbaseChartData.labels && airbaseChartData.labels.length > 0 && airbaseChartData.data && airbaseChartData.data.length > 0) {
        const airbaseCtx = airbaseCanvas.getContext('2d');
        new Chart(airbaseCtx, {
            type: 'bar',
            data: {
                labels: airbaseChartData.labels,
                datasets: [{
                    label: 'عدد المرشحين',
                    data: airbaseChartData.data,
                    backgroundColor: generateChartColors(airbaseChartData.data.length),
                    borderColor: generateBorderColors(generateChartColors(airbaseChartData.data.length)),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: { font: { family: 'Cairo' } }
                    },
                    x: {
                        ticks: { font: { family: 'Cairo' } }
                    }
                },
                plugins: {
                    legend: {
                        labels: { font: { family: 'Cairo' } }
                    }
                }
            }
        });
    } else if (airbaseCanvas) {
        showNoDataMessage('airbaseChart');
    }

    // Criteria Chart
    // These variables are expected to be defined in the CSHTML view:
    // criteriaChartData.labels, criteriaChartData.data
    const criteriaCanvas = document.getElementById('criteriaChart');

    if (criteriaCanvas && typeof criteriaChartData !== 'undefined' && criteriaChartData.labels && criteriaChartData.labels.length > 0 && criteriaChartData.data && criteriaChartData.data.length > 0) {
        const criteriaCtx = criteriaCanvas.getContext('2d');
        new Chart(criteriaCtx, {
            type: 'pie',
            data: {
                labels: criteriaChartData.labels,
                datasets: [{
                    label: 'متوسط الدرجة',
                    data: criteriaChartData.data,
                    backgroundColor: generateChartColors(criteriaChartData.data.length),
                    borderColor: generateBorderColors(generateChartColors(criteriaChartData.data.length)),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { font: { family: 'Cairo' } }
                    }
                }
            }
        });
    } else if (criteriaCanvas) {
        showNoDataMessage('criteriaChart');
    }

    // Category Chart
    // These variables are expected to be defined in the CSHTML view:
    // categoryChartData.labels, categoryChartData.data
    const categoryCanvas = document.getElementById('categoryChart');

    if (categoryCanvas && typeof categoryChartData !== 'undefined' && categoryChartData.labels && categoryChartData.labels.length > 0 && categoryChartData.data && categoryChartData.data.length > 0) {
        const categoryCtx = categoryCanvas.getContext('2d');
        new Chart(categoryCtx, {
            type: 'bar',
            data: {
                labels: categoryChartData.labels,
                datasets: [{
                    label: 'عدد المرشحين',
                    data: categoryChartData.data,
                    backgroundColor: generateChartColors(categoryChartData.data.length),
                    borderColor: generateBorderColors(generateChartColors(categoryChartData.data.length)),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: { font: { family: 'Cairo' } }
                    },
                    x: {
                        ticks: { font: { family: 'Cairo' } }
                    }
                },
                plugins: {
                    legend: {
                        labels: { font: { family: 'Cairo' } }
                    }
                }
            }
        });
    } else if (categoryCanvas) {
        showNoDataMessage('categoryChart');
    }
});