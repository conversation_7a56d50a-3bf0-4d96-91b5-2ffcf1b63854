using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using TajneedApp.Data;
using TajneedApp.Models;

namespace TajneedApp.Controllers
{
    public class CandidatesController : Controller
    {
        private readonly ApplicationDbContext _context;

        public CandidatesController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Candidates
        public async Task<IActionResult> Index()
        {
            var applicationDbContext = _context.Candidates.Include(c => c.Airbase).Include(c => c.Category).Include(c => c.Rank);
            return View(await applicationDbContext.ToListAsync());
        }

        // GET: Candidates/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var candidate = await _context.Candidates
                .Include(c => c.Airbase)
                .Include(c => c.Category)
                .Include(c => c.Rank)
                .FirstOrDefaultAsync(m => m.CandidateId == id);
            if (candidate == null)
            {
                return NotFound();
            }

            return View(candidate);
        }

        // GET: Candidates/Create
        public IActionResult Create()
        {
            ViewData["AirbaseId"] = new SelectList(_context.Airbase, "AirbaseId", "AirbaseName");
            ViewData["CategoryId"] = new SelectList(_context.Categories, "CategoryId", "CategoryCode");
            ViewData["RankId"] = new SelectList(_context.Ranks, "RankId", "RankName");
            return View();
        }

        // POST: Candidates/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("CandidateId,FullName,CategoryId,ServiceNumber,NationalIdNumber,RankId,AirbaseId,Department,Phone1,Phone2,Phone3,Major,University,GraduationYear,MarksGrade,IsActive,Address,DateOfBirth,JobTitle")] Candidate candidate)
        {
            if (ModelState.IsValid)
            {
                _context.Add(candidate);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            ViewData["AirbaseId"] = new SelectList(_context.Airbase, "AirbaseId", "AirbaseName", candidate.AirbaseId);
            ViewData["CategoryId"] = new SelectList(_context.Categories, "CategoryId", "CategoryCode", candidate.CategoryId);
            ViewData["RankId"] = new SelectList(_context.Ranks, "RankId", "RankName", candidate.RankId);
            return View(candidate);
        }

        // GET: Candidates/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var candidate = await _context.Candidates.FindAsync(id);
            if (candidate == null)
            {
                return NotFound();
            }
            ViewData["AirbaseId"] = new SelectList(_context.Airbase, "AirbaseId", "AirbaseName", candidate.AirbaseId);
            ViewData["CategoryId"] = new SelectList(_context.Categories, "CategoryId", "CategoryCode", candidate.CategoryId);
            ViewData["RankId"] = new SelectList(_context.Ranks, "RankId", "RankName", candidate.RankId);
            return View(candidate);
        }

        // POST: Candidates/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("CandidateId,FullName,CategoryId,ServiceNumber,NationalIdNumber,RankId,AirbaseId,Department,Phone1,Phone2,Phone3,Major,University,GraduationYear,MarksGrade,IsActive,Address,DateOfBirth,JobTitle")] Candidate candidate)
        {
            if (id != candidate.CandidateId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(candidate);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CandidateExists(candidate.CandidateId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["AirbaseId"] = new SelectList(_context.Airbase, "AirbaseId", "AirbaseName", candidate.AirbaseId);
            ViewData["CategoryId"] = new SelectList(_context.Categories, "CategoryId", "CategoryCode", candidate.CategoryId);
            ViewData["RankId"] = new SelectList(_context.Ranks, "RankId", "RankName", candidate.RankId);
            return View(candidate);
        }

        // GET: Candidates/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var candidate = await _context.Candidates
                .Include(c => c.Airbase)
                .Include(c => c.Category)
                .Include(c => c.Rank)
                .FirstOrDefaultAsync(m => m.CandidateId == id);
            if (candidate == null)
            {
                return NotFound();
            }

            return View(candidate);
        }

        // POST: Candidates/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var candidate = await _context.Candidates.FindAsync(id);
            if (candidate != null)
            {
                _context.Candidates.Remove(candidate);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool CandidateExists(int id)
        {
            return _context.Candidates.Any(e => e.CandidateId == id);
        }
    }
}
