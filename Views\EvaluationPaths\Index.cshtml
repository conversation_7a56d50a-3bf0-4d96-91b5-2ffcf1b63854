@model IEnumerable<TajneedApp.Models.EvaluationPath>

@{
    ViewData["Title"] = "مسارات التقييم";
}

<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h5 class="m-0 fw-bolder">إدارة مسارات التقييم</h5>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus"></i> إنشاء مسار تقييم جديد
            </a>
        </div>
        <div class="card-body">
            @if (TempData["Success"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>@TempData["Success"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            <div class="table-responsive">
                <table id="evaluationPathsTable" class="table table-bordered table-hover table-striped table-sm" style="font-size: 13px; width:100%;">
                    <thead class="table-light">
                        <tr>
                            <th>اسم المسار</th>
                            <th>الفئة</th>
                            <th>عدد المكونات</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>
                                    <strong>@Html.DisplayFor(modelItem => item.PathName)</strong>
                                    @if (!string.IsNullOrEmpty(item.Description))
                                    {
                                        <br><small class="text-muted">@Html.DisplayFor(modelItem => item.Description)</small>
                                    }
                                </td>
                                <td>
                                    <span class="badge bg-info">@Html.DisplayFor(modelItem => item.Category.CategoryName)</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">@item.Components.Count مكون</span>
                                </td>
                                <td>
                                    @if (item.IsActive)
                                    {
                                        <span class="badge bg-success">نشط</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-warning">غير نشط</span>
                                    }
                                </td>
                                <td>
                                    @item.CreatedDate.ToString("yyyy-MM-dd")
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a class="btn btn-sm btn-info" asp-action="Details" asp-route-id="@item.EvaluationPathId" title="التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a class="btn btn-sm btn-primary" asp-action="Edit" asp-route-id="@item.EvaluationPathId" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a class="btn btn-sm btn-secondary" asp-action="Clone" asp-route-id="@item.EvaluationPathId" title="نسخ">
                                            <i class="fas fa-copy"></i>
                                        </a>
                                        <a class="btn btn-sm btn-success" asp-controller="EvaluationComponents" asp-action="Index" asp-route-pathId="@item.EvaluationPathId" title="إدارة المكونات">
                                            <i class="fas fa-cogs"></i>
                                        </a>
                                        <a class="btn btn-sm btn-danger" asp-action="Delete" asp-route-id="@item.EvaluationPathId" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#evaluationPathsTable').DataTable({
                "scrollX": true,
                "language": {
                    "url": "/localization/ar.json"
                },
                "responsive": true,
                "autoWidth": true,
                "pageLength": 10,
                "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "الكل"]],
                "columnDefs": [
                    {
                        "orderable": false,
                        "targets": -1, // Actions column
                        "width": "200px"
                    },
                    {
                        "targets": 0, // Path name column
                        "width": "250px"
                    }
                ],
                "order": [[4, "desc"]] // Sort by creation date descending
            });
        });
    </script>
}
