@model IEnumerable<TajneedApp.Models.Category>

@{
    ViewData["Title"] = "Categories";
}

<div class="container py-5">
    <div class="card shadow-lg border-0 rounded-lg">
        <div class="card-header bg-gradient-primary text-white d-flex justify-content-between align-items-center py-4 px-4">
            <h2 class="m-0 font-weight-bold text-white"><i class="fas fa-layer-group me-2"></i> فئات المرشحين</h2>
   <a asp-action="Create" class="btn btn-outline-light btn-lg d-flex align-items-center rounded-pill shadow-sm animate__animated animate__fadeInRight">
                <i class="fas fa-plus-circle me-2"></i> إضافة فئة جديدة
            </a>
        </div>
        <div class="card-body p-4">
            <div class="table-responsive">
                <table class="table table-hover table-striped table-borderless align-middle" width="100%">
                    <thead class="bg-light">
                        <tr>
                            <th scope="col" class="text-primary">
                                @Html.DisplayNameFor(model => model.CategoryName)
                            </th>
                            <th scope="col" class="text-primary">
                                @Html.DisplayNameFor(model => model.Description)
                            </th>
                            <th scope="col" class="text-primary">
                                @Html.DisplayNameFor(model => model.CategoryCode)
                            </th>
                            <th scope="col" class="text-center text-primary">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr class="animate__animated animate__fadeInUp">
                                <td>
                                    @Html.DisplayFor(modelItem => item.CategoryName)
                                </td>
                                <td>
                                    @Html.DisplayFor(modelItem => item.Description)
                                </td>
                                <td>
                                    <span class="badge bg-info text-white p-2">@Html.DisplayFor(modelItem => item.CategoryCode)</span>
                                </td>
                                <td class="text-center text-nowrap">
                                    <a class="btn btn-outline-info btn-sm rounded-pill me-2 px-3 animate__animated animate__pulse" asp-action="Edit" asp-route-id="@item.CategoryId">
                                        <i class="fas fa-edit me-1"></i> تعديل
                                    </a>
                                    <a class="btn btn-outline-secondary btn-sm rounded-pill me-2 px-3 animate__animated animate__pulse" asp-action="Details" asp-route-id="@item.CategoryId">
                                        <i class="fas fa-info-circle me-1"></i> تفاصيل
                                    </a>
                                    <a class="btn btn-outline-danger btn-sm rounded-pill px-3 animate__animated animate__pulse" asp-action="Delete" asp-route-id="@item.CategoryId">
                                        <i class="fas fa-trash-alt me-1"></i> حذف
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/js/all.min.js" integrity="sha512-..." crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        .bg-gradient-primary {
            background: linear-gradient(45deg, #007bff, #0056b3) !important;
        }

        .btn-light {
            transition: all 0.3s ease;
        }

        .btn-light:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .table-hover tbody tr:hover {
            background-color: #f8f9fa;
            transform: scale(1.005);
            transition: all 0.2s ease-in-out;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .table td, .table th {
            padding: 1rem;
        }

        .rounded-lg {
            border-radius: 1rem !important;
        }

        .btn-outline-info,
        .btn-outline-secondary,
        .btn-outline-danger {
            border-width: 2px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-outline-info:hover {
            background-color: #0dcaf0;
            color: white;
            transform: translateY(-1px);
        }

        .btn-outline-secondary:hover {
            background-color: #6c757d;
            color: white;
            transform: translateY(-1px);
        }

        .btn-outline-danger:hover {
            background-color: #dc3545;
            color: white;
            transform: translateY(-1px);
        }

        .badge {
            font-size: 0.85em;
            padding: 0.6em 0.9em;
            border-radius: 0.5rem;
        }
    </style>
}