@model IEnumerable<TajneedApp.Models.Category>

@{
    ViewData["Title"] = "إدارة الفئات";
}

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary fw-bold mb-1">
                        <i class="fas fa-tags me-2"></i>إدارة فئات المرشحين
                    </h2>
                    <p class="text-muted mb-0">عرض وإدارة جميع فئات المرشحين في النظام</p>
                </div>
                <div>
                    <a asp-action="Create" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>إضافة فئة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="fas fa-tags text-primary fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="fw-bold text-primary mb-1">@Model.Count()</h3>
                    <p class="text-muted mb-0">إجمالي الفئات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="fas fa-check-circle text-success fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="fw-bold text-success mb-1">@Model.Count(c => !string.IsNullOrEmpty(c.Description))</h3>
                    <p class="text-muted mb-0">فئات مع وصف</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-info bg-opacity-10 p-3">
                            <i class="fas fa-code text-info fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="fw-bold text-info mb-1">@Model.Count(c => !string.IsNullOrEmpty(c.CategoryCode))</h3>
                    <p class="text-muted mb-0">فئات مع رمز</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                            <i class="fas fa-list text-warning fa-2x"></i>
                        </div>
                    </div>
                    <h3 class="fw-bold text-warning mb-1">@(Model.Any() ? Model.Max(c => c.CategoryName?.Length ?? 0) : 0)</h3>
                    <p class="text-muted mb-0">أطول اسم فئة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Table Card -->
    <div class="card shadow border-0">
        <div class="card-header bg-primary text-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>جدول الفئات
                    </h5>
                </div>
                <div class="col-auto">
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-list me-1"></i>@Model.Count() فئة
                    </span>
                </div>
            </div>
        </div>

        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="categoriesDataTable">
                    <thead class="table-dark">
                        <tr>
                            <th class="text-center" style="min-width: 150px;">
                                <i class="fas fa-tag me-1"></i>اسم الفئة
                            </th>
                            <th class="text-center" style="min-width: 200px;">
                                <i class="fas fa-info-circle me-1"></i>الوصف
                            </th>
                            <th class="text-center" style="min-width: 100px;">
                                <i class="fas fa-code me-1"></i>رمز الفئة
                            </th>
                            <th class="text-center no-sort" style="min-width: 150px;">
                                <i class="fas fa-cogs me-1"></i>الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td class="fw-bold text-center">
                                    @Html.DisplayFor(modelItem => item.CategoryName)
                                </td>
                                <td class="text-center">
                                    @if (!string.IsNullOrEmpty(item.Description))
                                    {
                                        <span class="text-muted">@Html.DisplayFor(modelItem => item.Description)</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted fst-italic">لا يوجد وصف</span>
                                    }
                                </td>
                                <td class="text-center">
                                    @if (!string.IsNullOrEmpty(item.CategoryCode))
                                    {
                                        <span class="badge bg-info">@Html.DisplayFor(modelItem => item.CategoryCode)</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">غير محدد</span>
                                    }
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@item.CategoryId"
                                           class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@item.CategoryId"
                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@item.CategoryId"
                                           class="btn btn-sm btn-outline-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذه الفئة؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Initialize DataTable with enhanced features
            var table = $('#categoriesDataTable').DataTable({
                "scrollX": true,
                "language": {
                    "url": "/localization/ar.json"
                },
                "responsive": {
                    "details": {
                        "type": 'column',
                        "target": 'tr'
                    }
                },
                "autoWidth": false,
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
                "dom": 'Bfrtip',
                "buttons": [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> تصدير Excel',
                        className: 'btn btn-success btn-sm',
                        exportOptions: {
                            columns: ':not(.no-export)'
                        }
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> تصدير PDF',
                        className: 'btn btn-danger btn-sm',
                        exportOptions: {
                            columns: ':not(.no-export)'
                        },
                        customize: function (doc) {
                            doc.defaultStyle.font = 'Arial';
                            doc.styles.tableHeader.alignment = 'center';
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> طباعة',
                        className: 'btn btn-info btn-sm',
                        exportOptions: {
                            columns: ':not(.no-export)'
                        }
                    },
                    {
                        extend: 'colvis',
                        text: '<i class="fas fa-columns"></i> إظهار/إخفاء الأعمدة',
                        className: 'btn btn-secondary btn-sm'
                    }
                ],
                "columnDefs": [
                    {
                        "targets": [0, 1, 2], // Category columns
                        "className": "text-center"
                    },
                    {
                        "targets": [-1], // Actions column
                        "orderable": false,
                        "searchable": false,
                        "className": "text-center no-export"
                    }
                ],
                "order": [[0, 'asc']], // Sort by Category Name by default
                "searching": true,
                "ordering": true,
                "info": true,
                "paging": true,
                "stateSave": true,
                "fixedHeader": true
            });

            // Add loading overlay
            table.on('processing.dt', function (e, settings, processing) {
                if (processing) {
                    $('.table-responsive').append('<div class="loading-overlay"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
                } else {
                    $('.loading-overlay').remove();
                }
            });

            // Responsive table adjustments
            $(window).on('resize', function () {
                table.columns.adjust().responsive.recalc();
            });

            // Add row click functionality for mobile
            $('#categoriesDataTable tbody').on('click', 'tr', function () {
                if ($(window).width() < 768) {
                    $(this).toggleClass('selected');
                }
            });

            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();

            // Enhanced delete confirmation
            $('a[asp-action="Delete"]').on('click', function(e) {
                e.preventDefault();
                var href = $(this).attr('href');

                Swal.fire({
                    title: 'تأكيد الحذف',
                    text: 'هل أنت متأكد من حذف هذه الفئة؟ لا يمكن التراجع عن هذا الإجراء.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'نعم، احذف',
                    cancelButtonText: 'إلغاء'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = href;
                    }
                });
            });
        });

        // Additional UI enhancements
        setTimeout(function() {
            // Style DataTable elements
            $('.dataTables_filter input').addClass('form-control form-control-sm');
            $('.dataTables_length select').addClass('form-select form-select-sm');
            $('.dataTables_filter input').attr('placeholder', 'البحث في الفئات...');
        }, 500);
    </script>
}