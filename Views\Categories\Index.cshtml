@model IEnumerable<TajneedApp.Models.Category>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.CategoryName)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Description)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.CategoryCode)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.CategoryName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Description)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.CategoryCode)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.CategoryId">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.CategoryId">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.CategoryId">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
