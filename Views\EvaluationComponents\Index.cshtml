@model IEnumerable<TajneedApp.Models.EvaluationComponent>

@{
    ViewData["Title"] = "مكونات التقييم";
    var evaluationPath = ViewData["EvaluationPath"] as TajneedApp.Models.EvaluationPath;
}

<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <div>
                <h5 class="m-0 fw-bolder">
                    @if (evaluationPath != null)
                    {
                        <span>مكونات التقييم - @evaluationPath.PathName</span>
                        <br><small class="text-muted">الفئة: @evaluationPath.Category?.CategoryName</small>
                    }
                    else
                    {
                        <span>إدارة مكونات التقييم</span>
                    }
                </h5>
            </div>
            <div>
                @if (evaluationPath != null)
                {
                    <a asp-action="Create" asp-route-pathId="@evaluationPath.EvaluationPathId" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة مكون جديد
                    </a>
                    <a asp-controller="EvaluationPaths" asp-action="Details" asp-route-id="@evaluationPath.EvaluationPathId" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للمسار
                    </a>
                }
                else
                {
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة مكون جديد
                    </a>
                }
            </div>
        </div>
        <div class="card-body">
            @if (TempData["Success"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>@TempData["Success"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            <div class="table-responsive">
                <table id="componentsTable" class="table table-bordered table-hover table-striped table-sm" style="font-size: 13px; width:100%;">
                    <thead class="table-light">
                        <tr>
                            <th>الترتيب</th>
                            <th>اسم المكون</th>
                            <th>المسار</th>
                            <th>النوع</th>
                            <th>نوع التقييم</th>
                            <th>الوزن %</th>
                            <th>المعايير</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>
                                    <span class="badge bg-primary">@item.DisplayOrder</span>
                                </td>
                                <td>
                                    <strong>@Html.DisplayFor(modelItem => item.ComponentName)</strong>
                                    @if (!string.IsNullOrEmpty(item.ComponentDescription))
                                    {
                                        <br><small class="text-muted">@Html.DisplayFor(modelItem => item.ComponentDescription)</small>
                                    }
                                </td>
                                <td>
                                    <span class="badge bg-info">@item.EvaluationPath?.Category?.CategoryName</span>
                                    <br><small>@item.EvaluationPath?.PathName</small>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">@item.ComponentType.GetDisplayName()</span>
                                </td>
                                <td>
                                    <span class="badge bg-dark">@item.EvaluationType.GetDisplayName()</span>
                                </td>
                                <td>
                                    <strong>@item.WeightPercentage%</strong>
                                </td>
                                <td>
                                    @if (item.ComponentType == ComponentType.CommitteeEvaluation)
                                    {
                                        <span class="badge bg-warning">@item.Criteria.Count معيار</span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                </td>
                                <td>
                                    @if (item.IsActive)
                                    {
                                        <span class="badge bg-success">نشط</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-warning">غير نشط</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a class="btn btn-sm btn-info" asp-action="Details" asp-route-id="@item.ComponentId" title="التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a class="btn btn-sm btn-primary" asp-action="Edit" asp-route-id="@item.ComponentId" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if (item.ComponentType == ComponentType.CommitteeEvaluation)
                                        {
                                            <a class="btn btn-sm btn-success" asp-controller="CommitteeEvaluationCriteria" asp-action="Index" asp-route-componentId="@item.ComponentId" title="إدارة المعايير">
                                                <i class="fas fa-list"></i>
                                            </a>
                                        }
                                        <a class="btn btn-sm btn-danger" asp-action="Delete" asp-route-id="@item.ComponentId" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            @if (evaluationPath != null)
            {
                @{
                    var pathComponents = Model.Where(c => c.EvaluationPathId == evaluationPath.EvaluationPathId && c.IsActive);
                    var totalWeight = pathComponents.Sum(c => c.WeightPercentage);
                }
                <div class="mt-3">
                    <div class="alert @(totalWeight == 100 ? "alert-success" : "alert-warning")">
                        <i class="fas fa-calculator me-2"></i>
                        <strong>إجمالي أوزان المكونات النشطة:</strong> @totalWeight%
                        @if (totalWeight != 100)
                        {
                            <span class="ms-2">(يجب أن يكون المجموع 100%)</span>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#componentsTable').DataTable({
                "scrollX": true,
                "language": {
                    "url": "/localization/ar.json"
                },
                "responsive": true,
                "autoWidth": true,
                "pageLength": 10,
                "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "الكل"]],
                "columnDefs": [
                    {
                        "orderable": false,
                        "targets": -1, // Actions column
                        "width": "200px"
                    },
                    {
                        "targets": 0, // Order column
                        "width": "80px"
                    }
                ],
                "order": [[0, "asc"]] // Sort by display order
            });
        });
    </script>
}
