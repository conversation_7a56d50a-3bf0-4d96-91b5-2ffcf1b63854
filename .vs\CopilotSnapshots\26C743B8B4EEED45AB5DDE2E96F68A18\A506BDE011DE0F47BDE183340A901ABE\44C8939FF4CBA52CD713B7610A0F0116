﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using TajneedApp.Models;

namespace TajneedApp.Data;

public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public DbSet<Rank> Ranks { get; set; }
    public DbSet<Airbase> Airbase { get; set; }
    public DbSet<Category> Categories { get; set; }
    public DbSet<Exam> Exams { get; set; }
    public DbSet<Candidate> Candidates { get; set; }
  
  
    

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.Entity<ApplicationUser>()
            .HasIndex(u => u.ServiceNumber)
            .IsUnique();

        builder.Entity<ApplicationUser>()
            .HasOne(u => u.Rank)
            .WithMany(r => r.Users)
            .HasForeignKey(u => u.RankId);
    }
}
