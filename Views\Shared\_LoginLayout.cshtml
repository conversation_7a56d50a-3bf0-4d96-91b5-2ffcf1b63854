<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام التجنيد</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }

        /* Custom rounded corners for modern look */
        .rounded-4 {
            border-radius: 1.5rem !important;
        }

        /* Enhanced button hover effects */
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
        }

        /* Input focus effects */
        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        /* Card shadow enhancement */
        .shadow-lg {
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
        }

        /* Background gradient */
        .bg-gradient {
            background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%) !important;
        }

        /* Smooth transitions */
        .btn, .form-control, .input-group {
            transition: all 0.3s ease;
        }

        /* Responsive adjustments */
        @@media (max-width: 768px) {
            .card-body {
                padding: 2rem !important;
            }
        }

        @@media (max-width: 576px) {
            .card-body {
                padding: 1.5rem !important;
            }
        }
    </style>
</head>
<body>
    <main role="main">
        @RenderBody()
    </main>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
