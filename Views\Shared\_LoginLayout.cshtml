<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام التجنيد</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/custom.css" asp-append-version="true" />
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        /* Modern Login Page Styles */
        :root {
            --primary-color: #1e40af;
            --primary-dark: #1e3a8a;
            --primary-light: #3b82f6;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #06b6d4;
            --light-color: #f8fafc;
            --dark-color: #0f172a;
            --border-color: #e2e8f0;
            --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body.login-body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        .login-page-wrapper {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .login-background-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .bg-element {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transition: all 2s ease;
        }

        .bg-element.animate {
            opacity: 1;
        }

        .bg-element-1 {
            width: 300px;
            height: 300px;
            top: 10%;
            right: 10%;
            animation: float 6s ease-in-out infinite;
        }

        .bg-element-2 {
            width: 200px;
            height: 200px;
            bottom: 20%;
            left: 15%;
            animation: float 8s ease-in-out infinite reverse;
        }

        .bg-element-3 {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 20%;
            animation: float 10s ease-in-out infinite;
        }

        @@keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .login-container {
            width: 100%;
            max-width: 1200px;
            position: relative;
            z-index: 2;
        }

        .login-card-wrapper {
            display: grid;
            grid-template-columns: 1fr 400px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            min-height: 600px;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .login-card-wrapper.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .login-card {
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            gap: 20px;
        }

        .logo-container {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-medium);
            position: relative;
        }

        .logo-overlay i {
            font-size: 2.5rem;
            color: white;
        }

        .brand-info {
            text-align: right;
        }

        .brand-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 5px;
        }

        .brand-subtitle {
            font-size: 0.9rem;
            color: var(--secondary-color);
            margin: 0;
        }

        .welcome-message {
            margin-bottom: 20px;
        }

        .welcome-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 8px;
        }

        .welcome-subtitle {
            color: var(--secondary-color);
            font-size: 0.95rem;
            margin: 0;
        }

        .login-form-section {
            flex: 1;
        }

        .form-group-modern {
            margin-bottom: 25px;
        }

        /* Enhanced Bootstrap Input Groups */
        .input-group {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
        }

        .input-group:hover {
            box-shadow: var(--shadow-medium);
        }

        .input-group-text {
            border: none;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            color: var(--secondary-color);
            font-size: 1.1rem;
            padding: 12px 15px;
        }

        .form-control {
            border: none;
            background: white;
            font-size: 1rem;
            color: var(--dark-color);
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            box-shadow: none;
            border-color: transparent;
            background: white;
        }

        .form-label {
            color: var(--dark-color);
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .form-label i {
            color: var(--primary-color);
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--secondary-color);
            cursor: pointer;
            padding: 5px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            color: var(--primary-color);
            background: rgba(30, 64, 175, 0.1);
        }

        .remember-section {
            margin: 30px 0;
        }

        .custom-checkbox-modern {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .checkbox-modern {
            display: none;
        }

        .checkbox-indicator {
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .checkbox-modern:checked + .checkbox-label-modern .checkbox-indicator {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .checkbox-modern:checked + .checkbox-label-modern .checkbox-indicator::after {
            content: '\f00c';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            color: white;
            font-size: 0.8rem;
        }

        .checkbox-label-modern {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            color: var(--secondary-color);
            font-size: 0.95rem;
        }

        .submit-section {
            margin-top: 35px;
        }

        /* Enhanced Bootstrap Buttons */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border: none;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-medium);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-2px);
            box-shadow: var(--shadow-large);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-primary:disabled {
            cursor: not-allowed;
            opacity: 0.7;
            transform: none;
        }

        .btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            transition: opacity 0.3s ease;
        }

        .btn-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .login-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            color: var(--secondary-color);
        }

        .security-badge {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .security-badge i {
            color: var(--success-color);
        }

        .side-panel {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            color: white;
            padding: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            opacity: 0;
            transform: translateX(30px);
            transition: all 0.8s ease 0.3s;
        }

        .side-panel.animate-in {
            opacity: 1;
            transform: translateX(0);
        }

        .side-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .side-content {
            position: relative;
            z-index: 1;
            text-align: center;
        }

        .side-icon {
            font-size: 4rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .side-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .side-description {
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .side-features {
            text-align: right;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            font-size: 0.95rem;
        }

        .feature-item i {
            color: var(--success-color);
            font-size: 1.1rem;
        }

        .alert-modern {
            border: none;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 25px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            background: rgba(239, 68, 68, 0.1);
            border-left: 4px solid var(--danger-color);
        }

        .alert-icon {
            color: var(--danger-color);
            font-size: 1.2rem;
            margin-top: 2px;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-size: 0.95rem;
            font-weight: 600;
            color: var(--danger-color);
            margin-bottom: 5px;
        }

        .alert-messages {
            font-size: 0.9rem;
            color: var(--danger-color);
        }

        .field-validation-modern {
            display: block;
            color: var(--danger-color);
            font-size: 0.85rem;
            margin-top: 8px;
            margin-right: 45px;
        }

        /* Responsive Design */
        @@media (max-width: 768px) {
            .login-card-wrapper {
                grid-template-columns: 1fr;
                margin: 10px;
            }

            .side-panel {
                order: -1;
                padding: 30px;
                min-height: 200px;
            }

            .login-card {
                padding: 30px 20px;
            }

            .logo-section {
                flex-direction: column;
                gap: 15px;
            }

            .brand-info {
                text-align: center;
            }

            .brand-title {
                font-size: 1.5rem;
            }

            .side-icon {
                font-size: 3rem;
                margin-bottom: 20px;
            }

            .side-title {
                font-size: 1.5rem;
            }

            .side-description {
                font-size: 0.9rem;
                margin-bottom: 30px;
            }

            .side-features {
                text-align: center;
            }
        }

        @@media (max-width: 480px) {
            .login-page-wrapper {
                padding: 10px;
            }

            .login-card {
                padding: 20px 15px;
            }

            .form-group-modern {
                margin-bottom: 20px;
            }

            .input-container {
                border-radius: 10px;
            }

            .btn-login-modern {
                padding: 14px;
                border-radius: 10px;
            }
        }
    </style>
</head>
<body class="login-body">
    <main role="main">
        @RenderBody()
    </main>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
