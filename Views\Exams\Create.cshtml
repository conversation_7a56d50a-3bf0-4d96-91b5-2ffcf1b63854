@model TajneedApp.Models.Exam

@{
    ViewData["Title"] = "إضافة امتحان جديد";
}

<h1>Create</h1>

<h4>Exam</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="ExamName" class="control-label"></label>
                <input asp-for="ExamName" class="form-control" />
                <span asp-validation-for="ExamName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CategoryId" class="control-label"></label>
                <select asp-for="CategoryId" class ="form-control" asp-items="ViewBag.CategoryId"></select>
            </div>
               <div class="mb-3">
                    <label asp-for="EvaluationType" class="form-label"></label>
                    <select asp-for="EvaluationType" 
                        class="form-control" 
                        asp-items="Html.GetEnumSelectList<EvaluationType>()"
                        id="evaluationType">
                        <option value="">اختر نوع التقييم</option>
                    </select>
                    <span asp-validation-for="EvaluationType" class="text-danger"></span>
                </div>
            <div class="form-group">
                <label asp-for="PassingScore" class="control-label"></label>
                <input asp-for="PassingScore" class="form-control" />
                <span asp-validation-for="PassingScore" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="MaxScore" class="control-label"></label>
                <input asp-for="MaxScore" class="form-control" />
                <span asp-validation-for="MaxScore" class="text-danger"></span>
            </div> 
  
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
