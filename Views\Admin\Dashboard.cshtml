@{
    ViewData["Title"] = "لوحة التحكم";
    var candidatesByCategory = ViewData["CandidatesByCategory"] as List<dynamic>;
    var candidatesByRank = ViewData["CandidatesByRank"] as List<dynamic>;
    var candidatesByAirbase = ViewData["CandidatesByAirbase"] as List<dynamic>;
    var candidatesByYear = ViewData["CandidatesByYear"] as List<dynamic>;
    var ageGroups = ViewData["AgeGroups"] as List<dynamic>;
    var topUniversities = ViewData["TopUniversities"] as List<dynamic>;
    var topMajors = ViewData["TopMajors"] as List<dynamic>;
    var recentCandidates = ViewData["RecentCandidates"] as List<TajneedApp.Models.Candidate>;
}

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-primary fw-bold mb-1">
                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم الرئيسية
                    </h1>
                    <p class="text-muted mb-0">نظرة شاملة على إحصائيات وبيانات النظام</p>
                </div>
                <div>
                    <span class="badge bg-success fs-6">
                        <i class="fas fa-clock me-1"></i>آخر تحديث: @DateTime.Now.ToString("yyyy/MM/dd HH:mm")
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 bg-gradient-primary text-white">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-white bg-opacity-20 p-3">
                            <i class="fas fa-users text-white fa-3x"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1">@ViewData["CandidatesCount"]</h2>
                    <p class="mb-0 opacity-75">إجمالي المرشحين</p>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-white" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 bg-gradient-success text-white">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-white bg-opacity-20 p-3">
                            <i class="fas fa-tags text-white fa-3x"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1">@ViewData["CategoriesCount"]</h2>
                    <p class="mb-0 opacity-75">فئات المرشحين</p>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-white" style="width: 85%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 bg-gradient-info text-white">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-white bg-opacity-20 p-3">
                            <i class="fas fa-list text-white fa-3x"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1">@ViewData["RanksCount"]</h2>
                    <p class="mb-0 opacity-75">الرتب العسكرية</p>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-white" style="width: 70%"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100 bg-gradient-warning text-white">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <div class="rounded-circle bg-white bg-opacity-20 p-3">
                            <i class="fas fa-plane text-white fa-3x"></i>
                        </div>
                    </div>
                    <h2 class="fw-bold mb-1">@ViewData["AirbasesCount"]</h2>
                    <p class="mb-0 opacity-75">القواعد الجوية</p>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-white" style="width: 60%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Overview -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="fas fa-check-circle me-2"></i>حالة المرشحين</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-success fw-bold">نشط</span>
                        <span class="badge bg-success">@ViewData["ActiveCandidates"]</span>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar bg-success" style="width: @(((int)ViewData["ActiveCandidates"] * 100) / Math.Max((int)ViewData["CandidatesCount"], 1))%"></div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-danger fw-bold">غير نشط</span>
                        <span class="badge bg-danger">@ViewData["InactiveCandidates"]</span>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar bg-danger" style="width: @(((int)ViewData["InactiveCandidates"] * 100) / Math.Max((int)ViewData["CandidatesCount"], 1))%"></div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-secondary fw-bold">غير محدد</span>
                        <span class="badge bg-secondary">@ViewData["UndefinedCandidates"]</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-secondary" style="width: @(((int)ViewData["UndefinedCandidates"] * 100) / Math.Max((int)ViewData["CandidatesCount"], 1))%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-university me-2"></i>أفضل الجامعات</h6>
                </div>
                <div class="card-body">
                    @if (topUniversities != null && topUniversities.Any())
                    {
                        @foreach (var university in topUniversities)
                        {
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-bold text-truncate" style="max-width: 200px;">@university.University</span>
                                <span class="badge bg-info">@university.Count</span>
                            </div>
                            <div class="progress mb-3" style="height: 6px;">
                                <div class="progress-bar bg-info" style="width: @((university.Count * 100) / topUniversities.Max(u => u.Count))%"></div>
                            </div>
                        }
                    }
                    else
                    {
                        <p class="text-muted text-center">لا توجد بيانات جامعات</p>
                    }
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>أفضل التخصصات</h6>
                </div>
                <div class="card-body">
                    @if (topMajors != null && topMajors.Any())
                    {
                        @foreach (var major in topMajors)
                        {
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-bold text-truncate" style="max-width: 200px;">@major.Major</span>
                                <span class="badge bg-warning">@major.Count</span>
                            </div>
                            <div class="progress mb-3" style="height: 6px;">
                                <div class="progress-bar bg-warning" style="width: @((major.Count * 100) / topMajors.Max(m => m.Count))%"></div>
                            </div>
                        }
                    }
                    else
                    {
                        <p class="text-muted text-center">لا توجد بيانات تخصصات</p>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>توزيع المرشحين حسب الفئة</h6>
                </div>
                <div class="card-body">
                    <canvas id="categoryChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>توزيع المرشحين حسب القاعدة الجوية</h6>
                </div>
                <div class="card-body">
                    <canvas id="airbaseChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Charts -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-chart-line me-2"></i>توزيع المرشحين حسب سنة التخرج</h6>
                </div>
                <div class="card-body">
                    <canvas id="yearChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0"><i class="fas fa-chart-doughnut me-2"></i>توزيع الأعمار</h6>
                </div>
                <div class="card-body">
                    <canvas id="ageChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Candidates Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-dark text-white">
                    <h6 class="mb-0"><i class="fas fa-clock me-2"></i>أحدث المرشحين المضافين</h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>الاسم</th>
                                    <th>الرتبة</th>
                                    <th>الفئة</th>
                                    <th>القاعدة الجوية</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (recentCandidates != null && recentCandidates.Any())
                                {
                                    @foreach (var candidate in recentCandidates)
                                    {
                                        <tr>
                                            <td class="fw-bold">@candidate.FullName</td>
                                            <td>@(candidate.Rank?.RankName ?? "غير محدد")</td>
                                            <td><span class="badge bg-primary">@(candidate.Category?.CategoryName ?? "غير محدد")</span></td>
                                            <td>@(candidate.Airbase?.AirbaseName ?? "غير محدد")</td>
                                            <td>
                                                @if (candidate.IsActive == 1)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else if (candidate.IsActive == 0)
                                                {
                                                    <span class="badge bg-danger">غير نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">لا توجد بيانات مرشحين</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        $(document).ready(function() {
            // Category Chart (Pie Chart)
            var categoryData = @Html.Raw(Json.Serialize(candidatesByCategory ?? new List<object>()));
            console.log('Category Data:', categoryData);

            if (categoryData && categoryData.length > 0) {
                var categoryCtx = document.getElementById('categoryChart').getContext('2d');
                new Chart(categoryCtx, {
                    type: 'pie',
                    data: {
                        labels: categoryData.map(item => item.Category || item.category || 'غير محدد'),
                        datasets: [{
                            data: categoryData.map(item => item.Count || item.count || 0),
                            backgroundColor: [
                                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                                '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            } else {
                // Show sample data if no real data
                var categoryCtx = document.getElementById('categoryChart').getContext('2d');
                new Chart(categoryCtx, {
                    type: 'pie',
                    data: {
                        labels: ['ضباط', 'ضباط صف', 'جنود', 'مدنيين'],
                        datasets: [{
                            data: [25, 35, 20, 20],
                            backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // Airbase Chart (Bar Chart)
            var airbaseData = @Html.Raw(Json.Serialize(candidatesByAirbase ?? new List<object>()));
            console.log('Airbase Data:', airbaseData);

            if (airbaseData && airbaseData.length > 0) {
                var airbaseCtx = document.getElementById('airbaseChart').getContext('2d');
                new Chart(airbaseCtx, {
                    type: 'bar',
                    data: {
                        labels: airbaseData.map(item => item.Airbase || item.airbase || 'غير محدد'),
                        datasets: [{
                            label: 'عدد المرشحين',
                            data: airbaseData.map(item => item.Count || item.count || 0),
                            backgroundColor: '#28a745',
                            borderColor: '#1e7e34',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            } else {
                // Show sample data if no real data
                var airbaseCtx = document.getElementById('airbaseChart').getContext('2d');
                new Chart(airbaseCtx, {
                    type: 'bar',
                    data: {
                        labels: ['قاعدة الملك عبدالعزيز', 'قاعدة الملك فهد', 'قاعدة الملك خالد', 'قاعدة تبوك'],
                        datasets: [{
                            label: 'عدد المرشحين',
                            data: [45, 32, 28, 15],
                            backgroundColor: '#28a745',
                            borderColor: '#1e7e34',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            // Year Chart (Line Chart)
            var yearData = @Html.Raw(Json.Serialize(candidatesByYear ?? new List<object>()));
            console.log('Year Data:', yearData);

            if (yearData && yearData.length > 0) {
                var yearCtx = document.getElementById('yearChart').getContext('2d');
                new Chart(yearCtx, {
                    type: 'line',
                    data: {
                        labels: yearData.map(item => item.Year || item.year || 'غير محدد'),
                        datasets: [{
                            label: 'عدد المرشحين',
                            data: yearData.map(item => item.Count || item.count || 0),
                            borderColor: '#17a2b8',
                            backgroundColor: 'rgba(23, 162, 184, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            } else {
                // Show sample data if no real data
                var yearCtx = document.getElementById('yearChart').getContext('2d');
                new Chart(yearCtx, {
                    type: 'line',
                    data: {
                        labels: ['2020', '2021', '2022', '2023', '2024'],
                        datasets: [{
                            label: 'عدد المرشحين',
                            data: [12, 19, 25, 32, 28],
                            borderColor: '#17a2b8',
                            backgroundColor: 'rgba(23, 162, 184, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            // Age Chart (Doughnut Chart)
            var ageData = @Html.Raw(Json.Serialize(ageGroups ?? new List<object>()));
            console.log('Age Data:', ageData);

            if (ageData && ageData.length > 0) {
                var ageCtx = document.getElementById('ageChart').getContext('2d');
                new Chart(ageCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ageData.map(item => item.AgeGroup || item.ageGroup || 'غير محدد'),
                        datasets: [{
                            data: ageData.map(item => item.Count || item.count || 0),
                            backgroundColor: [
                                '#ffc107', '#fd7e14', '#dc3545', '#6f42c1', '#20c997'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            } else {
                // Show sample data if no real data
                var ageCtx = document.getElementById('ageChart').getContext('2d');
                new Chart(ageCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['أقل من 25', '25-29', '30-34', '35-39', '40+'],
                        datasets: [{
                            data: [15, 25, 30, 20, 10],
                            backgroundColor: [
                                '#ffc107', '#fd7e14', '#dc3545', '#6f42c1', '#20c997'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // Add animation to cards
            $('.card').each(function(index) {
                $(this).css('animation-delay', (index * 0.1) + 's');
                $(this).addClass('animate__animated animate__fadeInUp');
            });

            // Add hover effects to progress bars
            $('.progress-bar').hover(
                function() {
                    $(this).css('transform', 'scaleY(1.1)');
                },
                function() {
                    $(this).css('transform', 'scaleY(1)');
                }
            );

            // Auto refresh every 5 minutes
            setTimeout(function() {
                location.reload();
            }, 300000);
        });
    </script>

    <style>
        .bg-gradient-primary {
            background: linear-gradient(135deg, #007bff, #0056b3) !important;
        }

        .bg-gradient-success {
            background: linear-gradient(135deg, #28a745, #1e7e34) !important;
        }

        .bg-gradient-info {
            background: linear-gradient(135deg, #17a2b8, #117a8b) !important;
        }

        .bg-gradient-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800) !important;
        }

        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
        }

        .progress-bar {
            transition: all 0.3s ease;
        }

        @@keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate__fadeInUp {
            animation: fadeInUp 0.6s ease-out;
        }

        canvas {
            max-height: 300px !important;
        }
    </style>
}