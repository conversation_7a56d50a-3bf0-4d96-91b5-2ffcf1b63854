@model TajneedApp.Data.ApplicationDbContext;

@{
    ViewData["Title"] = "لوحة التحكم";
}   

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary fw-bold mb-1">
                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                    </h2>
                    <p class="text-muted mb-0">عرض معلومات عامة عن النظام</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Cards Section -->
    <div class="row">
        <!-- Candidates Card -->
        <div class="col-md-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title">إجمالي المرشحين</h5>
                            <h4 class="card-text">@ViewData["CandidatesCount"]</h4>
                        </div>
                        <i class="fas fa-users fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
        <!-- Exams Card -->
        <div class="col-md-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title">إجمالي الامتحانات</h5>
                            <h4 class="card-text">@ViewData["ExamsCount"]</h4>
                        </div>
                        <i class="fas fa-file-alt fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
        <!-- Categories Card -->
        <div class="col-md-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title">إجمالي الفئات</h5>
                            <h4 class="card-text">@ViewData["CategoriesCount"]</h4>
                        </div>
                        <i class="fas fa-layer-group fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
        <!-- Ranks Card -->
        <div class="col-md-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title">إجمالي الرتب</h5>
                            <h4 class="card-text">@ViewData["RanksCount"]</h4>
                        </div>
                        <i class="fas fa-medal fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>  




</div>