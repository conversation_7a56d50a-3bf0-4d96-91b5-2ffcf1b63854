/* PAGING */

.pagination {
	text-align: center;
}

.pagination li {
	list-style-type: none ;
	float: left ;
}

.pagination li a {
    padding: 0 5px ;
    cursor: pointer ;
}

.pagination li.active a {
	cursor: default ;
	color: gray ;
}

/* SORT HEADER */

.sorting {
	cursor: pointer ;
	background-image: url("../img/sort_both.png") ;
    background-repeat: no-repeat;
    background-position: center right;
}

.sorting-desc {
	background-image: url("../img/sort_desc.png") ;
}

.sorting-asc {
	background-image: url("../img/sort_asc.png") ;
}

/* AJAX LOAD BAR */

.datatable-load-bar {
	width: 100% ;
	margin: 8px 0 ;
}

.datatable-load-bar {
	border: 1px solid gray ;
	height: 10px ;
}

.datatable-load-bar  .bar {
	height: 10px ;
	background-color: #AEF ;
}