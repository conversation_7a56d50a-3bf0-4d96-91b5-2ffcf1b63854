
@model IEnumerable<TajneedApp.Models.Exam>

@{
    ViewData["Title"] = "الامتحانات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-clipboard-list me-2"></i>قائمة الامتحانات
                    </h4>
                    <a asp-action="Create" class="btn btn-light">
                        <i class="fas fa-plus me-2"></i>إضافة امتحان جديد
                    </a>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th scope="col">
                                            <i class="fas fa-file-alt me-1"></i>@Html.DisplayNameFor(model => model.ExamName)
                                        </th>
                                        <th scope="col">
                                            <i class="fas fa-tags me-1"></i>@Html.DisplayNameFor(model => model.Category)
                                        </th>
                                        <th scope="col">
                                            <i class="fas fa-chart-line me-1"></i>@Html.DisplayNameFor(model => model.EvaluationType)
                                        </th>
                                        <th scope="col">
                                            <i class="fas fa-check-circle me-1"></i>@Html.DisplayNameFor(model => model.PassingScore)
                                        </th>
                                        <th scope="col">
                                            <i class="fas fa-star me-1"></i>@Html.DisplayNameFor(model => model.MaxScore)
                                        </th>
                                        <th scope="col" class="text-center">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model)
                                    {
                                        <tr>
                                            <td class="fw-bold">@Html.DisplayFor(modelItem => item.ExamName)</td>
                                            <td>
                                                <span class="badge bg-info">
                                                    @(item.Category?.CategoryName ?? "غير محدد")
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    @item.EvaluationTypeDisplayName
                                                </span>
                                            </td>
                                            <td>
                                                @if (item.PassingScore.HasValue)
                                                {
                                                    <span class="text-success fw-bold">@item.PassingScore</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                            <td>
                                                @if (item.MaxScore.HasValue)
                                                {
                                                    <span class="text-primary fw-bold">@item.MaxScore</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@item.ExamId"
                                                       class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.ExamId"
                                                       class="btn btn-outline-warning btn-sm" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@item.ExamId"
                                                       class="btn btn-outline-danger btn-sm" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد امتحانات مسجلة</h5>
                            <p class="text-muted">ابدأ بإضافة امتحان جديد</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة امتحان جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
