@model IEnumerable<TajneedApp.Models.Exam>

@{
    ViewData["Title"] = "الامتحانات";
}

<h1>الامتحانات</h1>

<p>
    <a asp-action="Create" class="btn btn-primary">إضافة امتحان جديد</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.ExamName)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.EvaluationType)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.PassingScore)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.MaxScore)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Category)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.ExamName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EvaluationType)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.PassingScore)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.MaxScore)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Category.CategoryName)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.ExamId">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.ExamId">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.ExamId">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
