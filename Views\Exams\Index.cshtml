
@model IEnumerable<TajneedApp.Models.Exam>

@{
    ViewData["Title"] = "الامتحانات";
}

<h1>الامتحانات</h1>

<p>
    <a asp-action="Create" class="btn btn-primary">إضافة امتحان جديد</a>
</p>
<table class="table" id="examsDataTable">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.ExamName)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.EvaluationType)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.PassingScore)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.MaxScore)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Category)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.ExamName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.EvaluationType)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.PassingScore)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.MaxScore)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Category.CategoryName)
            </td>
            <td>
                <a class="btn btn-sm btn-primary" asp-action="Edit" asp-route-id="@item.ExamId">Edit</a> |
                <a class="btn btn-sm btn-warning" asp-action="Details" asp-route-id="@item.ExamId">Details</a> |
                <a class="btn btn-sm btn-danger" asp-action="Delete" asp-route-id="@item.ExamId">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('#examsDataTable').DataTable({
                "scrollX": true,
                "language": {
                    "url": "/localization/ar.json" // Adjust path if your ar.json is elsewhere
                },
                "responsive": true,
                "autoWidth": true,
                "columnDefs": [
                    {
                        "orderable": true,
                        "targets": -1, // Targets the last column (actions)
                        "width": "180px"  // Fixed width for actions column
                    }
                  
                ]
            });
        });
    </script>
}
