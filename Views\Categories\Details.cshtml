@model TajneedApp.Models.Category

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Category</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.CategoryName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CategoryName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Description)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Description)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.CategoryCode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CategoryCode)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.CategoryId">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
