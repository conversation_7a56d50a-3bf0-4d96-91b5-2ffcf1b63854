@model TajneedApp.Models.Candidate

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Candidate</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.FullName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.FullName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.ServiceNumber)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.ServiceNumber)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.NationalIdNumber)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.NationalIdNumber)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Department)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Department)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Phone1)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Phone1)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Phone2)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Phone2)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Phone3)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Phone3)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Major)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Major)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.University)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.University)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.GraduationYear)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.GraduationYear)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.MarksGrade)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.MarksGrade)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.IsActive)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.IsActive)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Address)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Address)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.DateOfBirth)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.DateOfBirth)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.JobTitle)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.JobTitle)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Airbase)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Airbase.AirbaseName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Category)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Category.CategoryCode)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Rank)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Rank.RankName)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.CandidateId">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
