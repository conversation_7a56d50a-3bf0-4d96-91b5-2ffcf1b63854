@model TajneedApp.Models.Candidate

@{
    ViewData["Title"] = "Create";
}

<h1>Create</h1>

<h4>Candidate</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Create">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="FullName" class="control-label"></label>
                <input asp-for="FullName" class="form-control" />
                <span asp-validation-for="FullName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CategoryId" class="control-label"></label>
                <select asp-for="CategoryId" class ="form-control" asp-items="ViewBag.CategoryId"></select>
            </div>
            <div class="form-group">
                <label asp-for="ServiceNumber" class="control-label"></label>
                <input asp-for="ServiceNumber" class="form-control" />
                <span asp-validation-for="ServiceNumber" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="NationalIdNumber" class="control-label"></label>
                <input asp-for="NationalIdNumber" class="form-control" />
                <span asp-validation-for="NationalIdNumber" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="RankId" class="control-label"></label>
                <select asp-for="RankId" class ="form-control" asp-items="ViewBag.RankId"></select>
            </div>
            <div class="form-group">
                <label asp-for="AirbaseId" class="control-label"></label>
                <select asp-for="AirbaseId" class ="form-control" asp-items="ViewBag.AirbaseId"></select>
            </div>
            <div class="form-group">
                <label asp-for="Department" class="control-label"></label>
                <input asp-for="Department" class="form-control" />
                <span asp-validation-for="Department" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Phone1" class="control-label"></label>
                <input asp-for="Phone1" class="form-control" />
                <span asp-validation-for="Phone1" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Phone2" class="control-label"></label>
                <input asp-for="Phone2" class="form-control" />
                <span asp-validation-for="Phone2" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Phone3" class="control-label"></label>
                <input asp-for="Phone3" class="form-control" />
                <span asp-validation-for="Phone3" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Major" class="control-label"></label>
                <input asp-for="Major" class="form-control" />
                <span asp-validation-for="Major" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="University" class="control-label"></label>
                <input asp-for="University" class="form-control" />
                <span asp-validation-for="University" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="GraduationYear" class="control-label"></label>
                <input asp-for="GraduationYear" class="form-control" />
                <span asp-validation-for="GraduationYear" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="MarksGrade" class="control-label"></label>
                <input asp-for="MarksGrade" class="form-control" />
                <span asp-validation-for="MarksGrade" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="IsActive" class="control-label"></label>
                <input asp-for="IsActive" class="form-control" />
                <span asp-validation-for="IsActive" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Address" class="control-label"></label>
                <input asp-for="Address" class="form-control" />
                <span asp-validation-for="Address" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="DateOfBirth" class="control-label"></label>
                <input asp-for="DateOfBirth" class="form-control" />
                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="JobTitle" class="control-label"></label>
                <input asp-for="JobTitle" class="form-control" />
                <span asp-validation-for="JobTitle" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
