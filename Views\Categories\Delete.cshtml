@model TajneedApp.Models.Category

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Category</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.CategoryName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CategoryName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Description)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Description)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.CategoryCode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CategoryCode)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="CategoryId" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
