using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using TajneedApp.Data;
using TajneedApp.Models;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace TajneedApp.Controllers
{
    [Authorize]
    public class EvaluationPathsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public EvaluationPathsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: EvaluationPaths
        public async Task<IActionResult> Index()
        {
            var evaluationPaths = _context.EvaluationPaths
                .Include(e => e.Category)
                .Include(e => e.Components)
                .OrderBy(e => e.Category.CategoryName)
                .ThenBy(e => e.PathName);
            
            return View(await evaluationPaths.ToListAsync());
        }

        // GET: EvaluationPaths/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var evaluationPath = await _context.EvaluationPaths
                .Include(e => e.Category)
                .Include(e => e.Components.OrderBy(c => c.DisplayOrder))
                    .ThenInclude(c => c.Criteria.OrderBy(cr => cr.DisplayOrder))
                .FirstOrDefaultAsync(m => m.EvaluationPathId == id);

            if (evaluationPath == null)
            {
                return NotFound();
            }

            return View(evaluationPath);
        }

        // GET: EvaluationPaths/Create
        public IActionResult Create()
        {
            ViewData["CategoryId"] = new SelectList(_context.Categories.Where(c => c.CategoryName != null), "CategoryId", "CategoryName");
            return View();
        }

        // POST: EvaluationPaths/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("EvaluationPathId,PathName,Description,CategoryId,IsActive")] EvaluationPath evaluationPath)
        {
            if (ModelState.IsValid)
            {
                evaluationPath.CreatedDate = DateTime.UtcNow;
                evaluationPath.CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier);
                
                _context.Add(evaluationPath);
                await _context.SaveChangesAsync();
                
                TempData["Success"] = "تم إنشاء مسار التقييم بنجاح";
                return RedirectToAction(nameof(Details), new { id = evaluationPath.EvaluationPathId });
            }
            
            ViewData["CategoryId"] = new SelectList(_context.Categories.Where(c => c.CategoryName != null), "CategoryId", "CategoryName", evaluationPath.CategoryId);
            return View(evaluationPath);
        }

        // GET: EvaluationPaths/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var evaluationPath = await _context.EvaluationPaths.FindAsync(id);
            if (evaluationPath == null)
            {
                return NotFound();
            }
            
            ViewData["CategoryId"] = new SelectList(_context.Categories.Where(c => c.CategoryName != null), "CategoryId", "CategoryName", evaluationPath.CategoryId);
            return View(evaluationPath);
        }

        // POST: EvaluationPaths/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("EvaluationPathId,PathName,Description,CategoryId,IsActive,CreatedDate,CreatedBy")] EvaluationPath evaluationPath)
        {
            if (id != evaluationPath.EvaluationPathId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    evaluationPath.LastModifiedDate = DateTime.UtcNow;
                    evaluationPath.LastModifiedBy = User.FindFirstValue(ClaimTypes.NameIdentifier);
                    
                    _context.Update(evaluationPath);
                    await _context.SaveChangesAsync();
                    
                    TempData["Success"] = "تم تحديث مسار التقييم بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!EvaluationPathExists(evaluationPath.EvaluationPathId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Details), new { id = evaluationPath.EvaluationPathId });
            }
            
            ViewData["CategoryId"] = new SelectList(_context.Categories.Where(c => c.CategoryName != null), "CategoryId", "CategoryName", evaluationPath.CategoryId);
            return View(evaluationPath);
        }

        // GET: EvaluationPaths/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var evaluationPath = await _context.EvaluationPaths
                .Include(e => e.Category)
                .Include(e => e.Components)
                .FirstOrDefaultAsync(m => m.EvaluationPathId == id);

            if (evaluationPath == null)
            {
                return NotFound();
            }

            return View(evaluationPath);
        }

        // POST: EvaluationPaths/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var evaluationPath = await _context.EvaluationPaths.FindAsync(id);
            if (evaluationPath != null)
            {
                _context.EvaluationPaths.Remove(evaluationPath);
                await _context.SaveChangesAsync();
                TempData["Success"] = "تم حذف مسار التقييم بنجاح";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool EvaluationPathExists(int id)
        {
            return _context.EvaluationPaths.Any(e => e.EvaluationPathId == id);
        }

        // GET: EvaluationPaths/Clone/5
        public async Task<IActionResult> Clone(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var originalPath = await _context.EvaluationPaths
                .Include(e => e.Components)
                    .ThenInclude(c => c.Criteria)
                .FirstOrDefaultAsync(e => e.EvaluationPathId == id);

            if (originalPath == null)
            {
                return NotFound();
            }

            ViewData["CategoryId"] = new SelectList(_context.Categories.Where(c => c.CategoryName != null), "CategoryId", "CategoryName");
            ViewData["OriginalPath"] = originalPath;
            
            return View(new EvaluationPath 
            { 
                PathName = $"نسخة من {originalPath.PathName}",
                Description = originalPath.Description,
                IsActive = false
            });
        }

        // POST: EvaluationPaths/Clone
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Clone(int originalId, [Bind("PathName,Description,CategoryId,IsActive")] EvaluationPath newPath)
        {
            if (ModelState.IsValid)
            {
                var originalPath = await _context.EvaluationPaths
                    .Include(e => e.Components)
                        .ThenInclude(c => c.Criteria)
                    .FirstOrDefaultAsync(e => e.EvaluationPathId == originalId);

                if (originalPath == null)
                {
                    return NotFound();
                }

                // Create new path
                newPath.CreatedDate = DateTime.UtcNow;
                newPath.CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier);
                _context.EvaluationPaths.Add(newPath);
                await _context.SaveChangesAsync();

                // Clone components and criteria
                foreach (var originalComponent in originalPath.Components)
                {
                    var newComponent = new EvaluationComponent
                    {
                        ComponentName = originalComponent.ComponentName,
                        ComponentDescription = originalComponent.ComponentDescription,
                        EvaluationPathId = newPath.EvaluationPathId,
                        ComponentType = originalComponent.ComponentType,
                        EvaluationType = originalComponent.EvaluationType,
                        WeightPercentage = originalComponent.WeightPercentage,
                        MaxScore = originalComponent.MaxScore,
                        PassingScore = originalComponent.PassingScore,
                        DisplayOrder = originalComponent.DisplayOrder,
                        IsRequired = originalComponent.IsRequired,
                        IsActive = originalComponent.IsActive,
                        CreatedDate = DateTime.UtcNow,
                        CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier)
                    };
                    
                    _context.EvaluationComponents.Add(newComponent);
                    await _context.SaveChangesAsync();

                    // Clone criteria
                    foreach (var originalCriteria in originalComponent.Criteria)
                    {
                        var newCriteria = new CommitteeEvaluationCriteria
                        {
                            CriteriaName = originalCriteria.CriteriaName,
                            CriteriaDescription = originalCriteria.CriteriaDescription,
                            ComponentId = newComponent.ComponentId,
                            MaxScore = originalCriteria.MaxScore,
                            WeightPercentage = originalCriteria.WeightPercentage,
                            DisplayOrder = originalCriteria.DisplayOrder,
                            IsActive = originalCriteria.IsActive,
                            CreatedDate = DateTime.UtcNow,
                            CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier)
                        };
                        
                        _context.CommitteeEvaluationCriteria.Add(newCriteria);
                    }
                }

                await _context.SaveChangesAsync();
                TempData["Success"] = "تم نسخ مسار التقييم بنجاح";
                return RedirectToAction(nameof(Details), new { id = newPath.EvaluationPathId });
            }

            ViewData["CategoryId"] = new SelectList(_context.Categories.Where(c => c.CategoryName != null), "CategoryId", "CategoryName", newPath.CategoryId);
            return View(newPath);
        }
    }
}
