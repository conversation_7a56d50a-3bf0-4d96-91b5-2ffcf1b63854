using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TajneedApp.Data;
using TajneedApp.Models;

namespace TajneedApp.Controllers;

public class AdminController : Controller
{
    private readonly ApplicationDbContext _context;

    public AdminController(ApplicationDbContext context)
    {
        _context = context;
    }

    public IActionResult Index()
    {
        return View();
    }
    public IActionResult Dashboard()
    {
        ViewData["Title"] = "لوحة التحكم";

        // Basic Counts
        var candidatesCount = _context.Candidates.Count();
        var categoriesCount = _context.Categories.Count();
        var ranksCount = _context.Ranks.Count();
        var airbasesCount = _context.Airbase.Count();

        ViewData["CandidatesCount"] = candidatesCount;
        ViewData["CategoriesCount"] = categoriesCount;
        ViewData["RanksCount"] = ranksCount;
        ViewData["AirbasesCount"] = airbasesCount;

        // Candidates by Category
        var candidatesByCategory = _context.Candidates
            .Include(c => c.Category)
            .Where(c => c.Category != null)
            .GroupBy(c => c.Category!.CategoryName)
            .Select(g => new { Category = g.Key, Count = g.Count() })
            .ToList();
        ViewData["CandidatesByCategory"] = candidatesByCategory;

        // Candidates by Rank
        var candidatesByRank = _context.Candidates
            .Include(c => c.Rank)
            .Where(c => c.Rank != null)
            .GroupBy(c => c.Rank!.RankName)
            .Select(g => new { Rank = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .Take(10)
            .ToList();
        ViewData["CandidatesByRank"] = candidatesByRank;

        // Candidates by Airbase
        var candidatesByAirbase = _context.Candidates
            .Include(c => c.Airbase)
            .Where(c => c.Airbase != null)
            .GroupBy(c => c.Airbase!.AirbaseName)
            .Select(g => new { Airbase = g.Key, Count = g.Count() })
            .ToList();
        ViewData["CandidatesByAirbase"] = candidatesByAirbase;

        // Candidates by Graduation Year
        var candidatesByYear = _context.Candidates
            .Where(c => c.GraduationYear.HasValue)
            .GroupBy(c => c.GraduationYear.Value)
            .Select(g => new { Year = g.Key, Count = g.Count() })
            .OrderBy(x => x.Year)
            .ToList();
        ViewData["CandidatesByYear"] = candidatesByYear;

        // Active vs Inactive Candidates
        var activeCandidates = _context.Candidates.Count(c => c.IsActive == 1);
        var inactiveCandidates = _context.Candidates.Count(c => c.IsActive == 0);
        var undefinedCandidates = _context.Candidates.Count(c => c.IsActive == null);

        ViewData["ActiveCandidates"] = activeCandidates;
        ViewData["InactiveCandidates"] = inactiveCandidates;
        ViewData["UndefinedCandidates"] = undefinedCandidates;

        // Recent Candidates (last 10)
        var recentCandidates = _context.Candidates
            .OrderByDescending(c => c.CandidateId)
            .Take(10)
            .Include(c => c.Category)
            .Include(c => c.Rank)
            .Include(c => c.Airbase)
            .ToList();
        ViewData["RecentCandidates"] = recentCandidates;

        // Age Distribution (approximate based on graduation year)
        var currentYear = DateTime.Now.Year;
        var ageGroups = _context.Candidates
            .Where(c => c.GraduationYear.HasValue)
            .AsEnumerable()
            .GroupBy(c => {
                var age = currentYear - c.GraduationYear.Value + 22; // Assuming graduation at 22
                if (age < 25) return "أقل من 25";
                else if (age < 30) return "25-29";
                else if (age < 35) return "30-34";
                else if (age < 40) return "35-39";
                else return "40+";
            })
            .Select(g => new { AgeGroup = g.Key, Count = g.Count() })
            .ToList();
        ViewData["AgeGroups"] = ageGroups;

        // Top Universities
        var topUniversities = _context.Candidates
            .Where(c => !string.IsNullOrEmpty(c.University))
            .GroupBy(c => c.University)
            .Select(g => new { University = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .Take(5)
            .ToList();
        ViewData["TopUniversities"] = topUniversities;

        // Top Majors
        var topMajors = _context.Candidates
            .Where(c => !string.IsNullOrEmpty(c.Major))
            .GroupBy(c => c.Major)
            .Select(g => new { Major = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .Take(5)
            .ToList();
        ViewData["TopMajors"] = topMajors;

        return View();
    }

}
