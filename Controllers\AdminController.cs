using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TajneedApp.Data;
using TajneedApp.Models;

namespace TajneedApp.Controllers;

public class AdminController : Controller
{
    private readonly ApplicationDbContext _context;

    public AdminController(ApplicationDbContext context)
    {
        _context = context;
    }

    public IActionResult Index()
    {
        return View();
    }
    public IActionResult Dashboard()
    {
        ViewData["Title"] = "لوحة التحكم";
        // Add any necessary data to ViewData or ViewBag
        //use different charts and card to represent the information of the whole web application

        // number of Candidates

ViewData["CandidatesCount"] = _context.Candidates.Count();  

        // number of Exams
        // number of Categories
        // number of Ranks
        // number of Airbases
        // number of Users
        // number of Roles
        // number of Permissions
        // number of Logs
        // number of Settings
        // number of Reports
    

        return View();
    }

}
