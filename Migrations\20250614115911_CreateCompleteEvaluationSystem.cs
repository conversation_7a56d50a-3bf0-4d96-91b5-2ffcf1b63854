using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TajneedApp.Migrations
{
    /// <inheritdoc />
    public partial class CreateCompleteEvaluationSystem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "Exams",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "Exams",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "DisplayOrder",
                table: "Exams",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "Exams",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "WeightPercentage",
                table: "Exams",
                type: "decimal(5,2)",
                precision: 5,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.CreateTable(
                name: "CandidateExamResults",
                columns: table => new
                {
                    CandidateExamResultId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CandidateId = table.Column<int>(type: "int", nullable: false),
                    ExamId = table.Column<int>(type: "int", nullable: false),
                    Score = table.Column<int>(type: "int", nullable: false),
                    TextResult = table.Column<int>(type: "int", nullable: false),
                    ExamDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CandidateExamResults", x => x.CandidateExamResultId);
                    table.ForeignKey(
                        name: "FK_CandidateExamResults_Candidates_CandidateId",
                        column: x => x.CandidateId,
                        principalTable: "Candidates",
                        principalColumn: "CandidateId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CandidateExamResults_Exams_ExamId",
                        column: x => x.ExamId,
                        principalTable: "Exams",
                        principalColumn: "ExamId",
                        onDelete: ReferentialAction.NoAction);
                });

            migrationBuilder.CreateTable(
                name: "EvaluationPaths",
                columns: table => new
                {
                    EvaluationPathId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PathName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    CategoryId = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EvaluationPaths", x => x.EvaluationPathId);
                    table.ForeignKey(
                        name: "FK_EvaluationPaths_Categories_CategoryId",
                        column: x => x.CategoryId,
                        principalTable: "Categories",
                        principalColumn: "CategoryId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EvaluationComponents",
                columns: table => new
                {
                    ComponentId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ComponentName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ComponentDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    EvaluationPathId = table.Column<int>(type: "int", nullable: false),
                    ComponentType = table.Column<int>(type: "int", nullable: false),
                    EvaluationType = table.Column<int>(type: "int", nullable: false),
                    WeightPercentage = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: false),
                    MaxScore = table.Column<int>(type: "int", nullable: true),
                    PassingScore = table.Column<int>(type: "int", nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    IsRequired = table.Column<bool>(type: "bit", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EvaluationComponents", x => x.ComponentId);
                    table.ForeignKey(
                        name: "FK_EvaluationComponents_EvaluationPaths_EvaluationPathId",
                        column: x => x.EvaluationPathId,
                        principalTable: "EvaluationPaths",
                        principalColumn: "EvaluationPathId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CandidateComponentResults",
                columns: table => new
                {
                    ResultId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CandidateId = table.Column<int>(type: "int", nullable: false),
                    ComponentId = table.Column<int>(type: "int", nullable: false),
                    Score = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    TextResult = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    EvaluationDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EvaluatedBy = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Comments = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CandidateComponentResults", x => x.ResultId);
                    table.ForeignKey(
                        name: "FK_CandidateComponentResults_Candidates_CandidateId",
                        column: x => x.CandidateId,
                        principalTable: "Candidates",
                        principalColumn: "CandidateId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CandidateComponentResults_EvaluationComponents_ComponentId",
                        column: x => x.ComponentId,
                        principalTable: "EvaluationComponents",
                        principalColumn: "ComponentId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CandidateEvaluationProgress",
                columns: table => new
                {
                    ProgressId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CandidateId = table.Column<int>(type: "int", nullable: false),
                    EvaluationPathId = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CompletionDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    OverallScore = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: true),
                    WeightedScore = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: true),
                    CompletionPercentage = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: false),
                    CurrentComponentId = table.Column<int>(type: "int", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CandidateEvaluationProgress", x => x.ProgressId);
                    table.ForeignKey(
                        name: "FK_CandidateEvaluationProgress_Candidates_CandidateId",
                        column: x => x.CandidateId,
                        principalTable: "Candidates",
                        principalColumn: "CandidateId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CandidateEvaluationProgress_EvaluationComponents_CurrentComponentId",
                        column: x => x.CurrentComponentId,
                        principalTable: "EvaluationComponents",
                        principalColumn: "ComponentId",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_CandidateEvaluationProgress_EvaluationPaths_EvaluationPathId",
                        column: x => x.EvaluationPathId,
                        principalTable: "EvaluationPaths",
                        principalColumn: "EvaluationPathId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CommitteeEvaluationCriteria",
                columns: table => new
                {
                    CriteriaId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CriteriaName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    CriteriaDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    ComponentId = table.Column<int>(type: "int", nullable: false),
                    MaxScore = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: false),
                    WeightPercentage = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ExamId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CommitteeEvaluationCriteria", x => x.CriteriaId);
                    table.ForeignKey(
                        name: "FK_CommitteeEvaluationCriteria_EvaluationComponents_ComponentId",
                        column: x => x.ComponentId,
                        principalTable: "EvaluationComponents",
                        principalColumn: "ComponentId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CommitteeEvaluationCriteria_Exams_ExamId",
                        column: x => x.ExamId,
                        principalTable: "Exams",
                        principalColumn: "ExamId");
                });

            migrationBuilder.CreateTable(
                name: "CandidateCommitteeEvaluations",
                columns: table => new
                {
                    EvaluationId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CandidateId = table.Column<int>(type: "int", nullable: false),
                    CriteriaId = table.Column<int>(type: "int", nullable: false),
                    Score = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: false),
                    EvaluatedBy = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    EvaluationDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Comments = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CandidateCommitteeEvaluations", x => x.EvaluationId);
                    table.ForeignKey(
                        name: "FK_CandidateCommitteeEvaluations_Candidates_CandidateId",
                        column: x => x.CandidateId,
                        principalTable: "Candidates",
                        principalColumn: "CandidateId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CandidateCommitteeEvaluations_CommitteeEvaluationCriteria_CriteriaId",
                        column: x => x.CriteriaId,
                        principalTable: "CommitteeEvaluationCriteria",
                        principalColumn: "CriteriaId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CandidateCommitteeEvaluations_CandidateId",
                table: "CandidateCommitteeEvaluations",
                column: "CandidateId");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateCommitteeEvaluations_CriteriaId",
                table: "CandidateCommitteeEvaluations",
                column: "CriteriaId");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateComponentResults_CandidateId",
                table: "CandidateComponentResults",
                column: "CandidateId");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateComponentResults_ComponentId",
                table: "CandidateComponentResults",
                column: "ComponentId");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateEvaluationProgress_CandidateId",
                table: "CandidateEvaluationProgress",
                column: "CandidateId");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateEvaluationProgress_CurrentComponentId",
                table: "CandidateEvaluationProgress",
                column: "CurrentComponentId");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateEvaluationProgress_EvaluationPathId",
                table: "CandidateEvaluationProgress",
                column: "EvaluationPathId");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateExamResults_CandidateId",
                table: "CandidateExamResults",
                column: "CandidateId");

            migrationBuilder.CreateIndex(
                name: "IX_CandidateExamResults_ExamId",
                table: "CandidateExamResults",
                column: "ExamId");

            migrationBuilder.CreateIndex(
                name: "IX_CommitteeEvaluationCriteria_ComponentId",
                table: "CommitteeEvaluationCriteria",
                column: "ComponentId");

            migrationBuilder.CreateIndex(
                name: "IX_CommitteeEvaluationCriteria_ExamId",
                table: "CommitteeEvaluationCriteria",
                column: "ExamId");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationComponents_EvaluationPathId",
                table: "EvaluationComponents",
                column: "EvaluationPathId");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationPaths_CategoryId",
                table: "EvaluationPaths",
                column: "CategoryId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CandidateCommitteeEvaluations");

            migrationBuilder.DropTable(
                name: "CandidateComponentResults");

            migrationBuilder.DropTable(
                name: "CandidateEvaluationProgress");

            migrationBuilder.DropTable(
                name: "CandidateExamResults");

            migrationBuilder.DropTable(
                name: "CommitteeEvaluationCriteria");

            migrationBuilder.DropTable(
                name: "EvaluationComponents");

            migrationBuilder.DropTable(
                name: "EvaluationPaths");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "Exams");

            migrationBuilder.DropColumn(
                name: "DisplayOrder",
                table: "Exams");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "Exams");

            migrationBuilder.DropColumn(
                name: "WeightPercentage",
                table: "Exams");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "Exams");
        }
    }
}
