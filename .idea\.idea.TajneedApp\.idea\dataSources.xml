<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="TajneedApp@ALMAZIDI" uuid="f2a8e49e-801e-45da-8120-e9724a603620">
      <driver-ref>sqlserver.jb</driver-ref>
      <synchronize>true</synchronize>
      <configured-by-url>true</configured-by-url>
      <jdbc-driver>com.jetbrains.jdbc.sqlserver.SqlServerDriver</jdbc-driver>
      <jdbc-url>Server=ALMAZIDI;Database=TajneedApp;Trusted_Connection=True;TrustServerCertificate=True;MultipleActiveResultSets=true</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>