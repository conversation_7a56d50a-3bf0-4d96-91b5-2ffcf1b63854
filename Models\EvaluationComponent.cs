using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace TajneedApp.Models;

public enum ComponentType
{
    [Display(Name = "امتحان مكتوب")]
    WrittenExam = 1,
    [Display(Name = "امتحان عملي")]
    PracticalTest = 2,
    [Display(Name = "تقييم اللجنة")]
    CommitteeEvaluation = 3,
    [Display(Name = "مقابلة شخصية")]
    Interview = 4,
    [Display(Name = "فحص طبي")]
    MedicalExamination = 5,
    [Display(Name = "اختبار لياقة بدنية")]
    PhysicalFitness = 6
}

public partial class EvaluationComponent
{
    [Key]
    public int ComponentId { get; set; }

    [Display(Name = "اسم المكون")]
    [Required(ErrorMessage = "حقل اسم المكون مطلوب.")]
    [StringLength(200, ErrorMessage = "اسم المكون يجب أن لا يتجاوز 200 حرف.")]
    public string? ComponentName { get; set; }

    [Display(Name = "وصف المكون")]
    [StringLength(500, ErrorMessage = "وصف المكون يجب أن لا يتجاوز 500 حرف.")]
    public string? ComponentDescription { get; set; }

    [Display(Name = "معرف مسار التقييم")]
    [Required(ErrorMessage = "حقل معرف مسار التقييم مطلوب.")]
    public int EvaluationPathId { get; set; }

    [Display(Name = "نوع المكون")]
    [Required(ErrorMessage = "حقل نوع المكون مطلوب.")]
    public ComponentType ComponentType { get; set; }

    [Display(Name = "نوع التقييم")]
    [Required(ErrorMessage = "حقل نوع التقييم مطلوب.")]
    public EvaluationType EvaluationType { get; set; }

    [Display(Name = "نسبة الوزن (%)")]
    [Required(ErrorMessage = "حقل نسبة الوزن مطلوب.")]
    [Range(0.01, 100, ErrorMessage = "نسبة الوزن يجب أن تكون بين 0.01 و 100")]
    public decimal WeightPercentage { get; set; }

    [Display(Name = "الدرجة القصوى")]
    public int? MaxScore { get; set; }

    [Display(Name = "الدرجة المطلوبة للنجاح")]
    public int? PassingScore { get; set; }

    [Display(Name = "ترتيب العرض")]
    [Required(ErrorMessage = "حقل ترتيب العرض مطلوب.")]
    public int DisplayOrder { get; set; }

    [Display(Name = "إجباري")]
    public bool IsRequired { get; set; } = true;

    [Display(Name = "نشط")]
    public bool IsActive { get; set; } = true;

    [Display(Name = "تاريخ الإنشاء")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    [Display(Name = "المستخدم المنشئ")]
    public string? CreatedBy { get; set; }

    // Navigation Properties
    [ValidateNever]
    [Display(Name = "مسار التقييم")]
    public virtual EvaluationPath? EvaluationPath { get; set; }

    [ValidateNever]
    public virtual ICollection<CommitteeEvaluationCriteria> Criteria { get; set; } = new List<CommitteeEvaluationCriteria>();

    [ValidateNever]
    public virtual ICollection<CandidateComponentResult> ComponentResults { get; set; } = new List<CandidateComponentResult>();
}
