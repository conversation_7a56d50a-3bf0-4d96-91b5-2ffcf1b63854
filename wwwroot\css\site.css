@font-face {
  font-family: Cairo;
  src: url('../fonts/Cairo-Medium.ttf');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus,
.form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  font-size: 14px;
  font-family: 'Cairo', sans-serif;
  font-weight: 500;
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
  font-weight: 500;
  font-family: 'Cairo', sans-serif;
  overflow-x: hidden;
  background-color: #f8f9fa;
  color: #333333;
}

/* ==================== */
/* DATATABLE code */
/* ==================== */

/* Style the container for the pagination buttons */
.dataTables_wrapper .dataTables_paginate {
  text-align: right; /* Adjust alignment as needed */
  margin-top: 10px; /* Add some spacing above the buttons */
}

/* Style individual pagination buttons */
.dataTables_wrapper .dataTables_paginate .paginate_button {
  color: #0a2a73 !important; /* Blue text */
  border: 1px solid #2663a4; /* Blue border */
  background-color: white;
  margin-left: 5px;
  margin-right: 5px;
  border-radius: 5px;
  padding: 0.5em 1em;
  cursor: pointer; /* Indicate they are clickable */
  text-decoration: none !important; /* Remove underlines if they appear */
}

/* Style the hover effect on the buttons */
.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background-color: #2663a4; /* Darker blue on hover */
  color: white !important;
  border-color: #2663a4;
}

/* Style the "current" active button */
.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background-color: #0a2a73; /* Dark blue for the current page */
  color: white !important;
  border-color: #0a2a73;
}

/* Style the "disabled" buttons (if any) */
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
  color: #ccc !important;
  border-color: #ccc;
  background-color: #f8f8f8;
  cursor: not-allowed;
}

.sidebar {
  height: 100vh;
  position: fixed;
  top: 0;
  right: 0;
  width: 250px;
  background-color: #ffffff;
  padding-top: 0;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 0 15px rgba(0,0,0,0.05);
  overflow: hidden;
}

.sidebar.collapsed {
  width: 70px;
}

.main-content {
  margin-right: 250px;
  transition: all 0.3s ease;
  padding: 20px;
}

.main-content.expanded {
  margin-right: 80px;
}

.nav-link {
  padding: 12px 20px;
  color: #333333;
  display: flex;
  align-items: center;
  white-space: nowrap;
  border-radius: 5px;
  margin: 5px 10px;
  position: relative;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background-color: #194d81;
  color: #ffffff !important;
}

.nav-link:hover i {
  color: #ffffff !important;
}

.nav-link.active {
  background-color: #e9ecef;
  color: #000000;
}

.nav-link i {
  width: 24px;
  text-align: center;
  margin-left: 10px;
  font-size: 1.1rem;
  color: #1b6ec2;
  transition: color 0.3s ease;
}

.brand-container {
  padding: 20px;
  text-align: right;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
  color: #000000;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.toggle-btn {
  background: transparent;
  color: #666666;
  border: none;
  padding: 8px;
  cursor: pointer;
  margin-left: 10px;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  color: #000000;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.submenu {
  padding-right: 35px;
  background-color: inherit !important;
  border-radius: 5px;
  margin: 0 10px;
}

.submenu .nav-link {
  color: #555555;
  margin: 2px 0;
  padding: 10px 15px;
}

.submenu .nav-link:hover {
  background-color: #1a5a8f;
}

.nav-link[aria-expanded="true"] {
  color: #000000;
  background-color: #f8f9fa;
}

.nav-link[aria-expanded="true"]:hover {
  background-color: #194d81;
  color: #ffffff !important;
}

.nav-link[aria-expanded="true"] i.fa-chevron-down {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

.menu-text {
  transition: opacity 0.3s ease, visibility 0.3s ease;
  font-size: 0.9rem;
}

.collapsed .menu-text {
  opacity: 0;
  visibility: hidden;
  width: 0;
}

.collapsed .submenu {
  padding-right: 0;
  margin: 0;
}

.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

.nav-link:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(25, 77, 129, 0.3);
}

.sidebar.collapsed .nav-link {
  justify-content: center;
  padding: 12px 0;
  margin: 5px 5px;
}

.sidebar.collapsed .nav-link i {
  margin-left: 0;
  font-size: 1.2rem;
}

