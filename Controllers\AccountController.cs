using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TajneedApp.Data;
using TajneedApp.Models;
using TajneedApp.Models.AccountViewModels;

namespace TajneedApp.Controllers
{
    public class AccountController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly ApplicationDbContext _context;

        public AccountController(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            ApplicationDbContext context)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _context = context;
        }

        [HttpGet]
        public IActionResult Login()
        {
            if (User.Identity?.IsAuthenticated ?? false)
            {
                return RedirectToAction("Index", "Home");
            }
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Login(LoginViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = await _userManager.Users
                    .Include(u => u.Rank)
                    .FirstOrDefaultAsync(u => u.ServiceNumber == model.ServiceNumber);

                if (user != null)
                {
                    var result = await _signInManager.PasswordSignInAsync(
                        user.UserName!,
                        model.Password,
                        model.RememberMe,
                        lockoutOnFailure: false);

                    if (result.Succeeded)
                    {
                        // Add custom claims if not already present
                        var claims = await _userManager.GetClaimsAsync(user);
                        var claimsToAdd = new List<Claim>();

                        if (!claims.Any(c => c.Type == "FullName"))
                            claimsToAdd.Add(new Claim("FullName", user.FullName ?? ""));

                        if (!claims.Any(c => c.Type == "Rank"))
                        {
                            var rankName = user.Rank?.RankName ?? "";
                            claimsToAdd.Add(new Claim("Rank", rankName));
                        }

                        if (!claims.Any(c => c.Type == "ServiceNumber"))
                            claimsToAdd.Add(new Claim("ServiceNumber", user.ServiceNumber ?? ""));

                        if (!claims.Any(c => c.Type == "IsActive"))
                            claimsToAdd.Add(new Claim("IsActive", user.IsActive.ToString()));

                        if (claimsToAdd.Count > 0)
                            await _userManager.AddClaimsAsync(user, claimsToAdd);

                        // Re-sign in to update claims in cookie
                        await _signInManager.SignInAsync(user, isPersistent: model.RememberMe);

                        return RedirectToAction("Index", "Home");
                    }
                }

                ModelState.AddModelError(string.Empty, "محاولة دخول غير صحيحة. يرجى التحقق من الرقم العسكري وكلمة المرور.");
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout(string? returnUrl = null)
        {
            await _signInManager.SignOutAsync();
            if (returnUrl != null)
            {
                return LocalRedirect(returnUrl);
            }
            return RedirectToAction("Login", "Account");
        }

        public IActionResult Profile()
        {
            return View();
        }
    }
}
