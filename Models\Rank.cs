using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TajneedApp.Models
{
    public class Rank
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int RankId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string RankName { get; set; }
        
        
        public virtual ICollection<ApplicationUser> Users { get; set; }
    }
}
