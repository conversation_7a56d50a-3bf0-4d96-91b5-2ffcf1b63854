@model TajneedApp.Models.EvaluationPath

@{
    ViewData["Title"] = "حذف مسار التقييم";
}

<div class="container-fluid">
    <div class="card shadow mb-4 border-danger">
        <div class="card-header py-3 bg-danger text-white">
            <h3 class="m-0 font-weight-bold">
                <i class="fas fa-exclamation-triangle me-2"></i>
                تأكيد حذف مسار التقييم
            </h3>
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <h5><i class="fas fa-warning me-2"></i>تحذير!</h5>
                <p class="mb-0">
                    هل أنت متأكد من رغبتك في حذف مسار التقييم هذا؟ 
                    <strong>هذا الإجراء لا يمكن التراجع عنه</strong> وسيؤدي إلى حذف:
                </p>
                <ul class="mt-2 mb-0">
                    <li>جميع مكونات التقييم (@Model.Components.Count مكون)</li>
                    <li>جميع معايير التقييم المرتبطة</li>
                    <li>جميع نتائج التقييم للمرشحين</li>
                    <li>سجلات تقدم المرشحين في هذا المسار</li>
                </ul>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-sm-4">اسم المسار:</dt>
                        <dd class="col-sm-8">
                            <strong>@Html.DisplayFor(model => model.PathName)</strong>
                        </dd>
                        <dt class="col-sm-4">الفئة:</dt>
                        <dd class="col-sm-8">
                            <span class="badge bg-info">@Html.DisplayFor(model => model.Category.CategoryName)</span>
                        </dd>
                        <dt class="col-sm-4">الحالة:</dt>
                        <dd class="col-sm-8">
                            @if (Model.IsActive)
                            {
                                <span class="badge bg-success">نشط</span>
                            }
                            else
                            {
                                <span class="badge bg-warning">غير نشط</span>
                            }
                        </dd>
                    </dl>
                </div>
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-sm-4">عدد المكونات:</dt>
                        <dd class="col-sm-8">
                            <span class="badge bg-secondary">@Model.Components.Count مكون</span>
                        </dd>
                        <dt class="col-sm-4">تاريخ الإنشاء:</dt>
                        <dd class="col-sm-8">@Model.CreatedDate.ToString("yyyy-MM-dd HH:mm")</dd>
                        <dt class="col-sm-4">الوصف:</dt>
                        <dd class="col-sm-8">@Html.DisplayFor(model => model.Description)</dd>
                    </dl>
                </div>
            </div>

            @if (Model.Components.Any())
            {
                <div class="mt-4">
                    <h6 class="text-danger">المكونات التي ستحذف:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>اسم المكون</th>
                                    <th>النوع</th>
                                    <th>الوزن %</th>
                                    <th>المعايير</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var component in Model.Components.OrderBy(c => c.DisplayOrder))
                                {
                                    <tr>
                                        <td>@component.ComponentName</td>
                                        <td>
                                            <span class="badge bg-info">@component.ComponentType.GetDisplayName()</span>
                                        </td>
                                        <td>@component.WeightPercentage%</td>
                                        <td>
                                            @if (component.ComponentType == ComponentType.CommitteeEvaluation)
                                            {
                                                <span class="badge bg-warning">@component.Criteria.Count معيار</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            }

            <div class="mt-4 pt-3 border-top">
                <form asp-action="Delete" method="post" class="d-inline">
                    <input type="hidden" asp-for="EvaluationPathId" />
                    <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من رغبتك في حذف هذا المسار نهائياً؟')">
                        <i class="fas fa-trash"></i> نعم، احذف المسار نهائياً
                    </button>
                </form>
                <a asp-action="Details" asp-route-id="@Model.EvaluationPathId" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> إلغاء والعودة للتفاصيل
                </a>
                <a asp-action="Index" class="btn btn-outline-secondary">
                    <i class="fas fa-list"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>
