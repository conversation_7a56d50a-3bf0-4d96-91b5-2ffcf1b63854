@model TajneedApp.Models.Rank

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Rank</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.RankName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.RankName)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.RankId">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
