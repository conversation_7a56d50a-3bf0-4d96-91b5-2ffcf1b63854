@model TajneedApp.Models.Airbase

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Airbase</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AirbaseName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AirbaseName)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.AirbaseId">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
