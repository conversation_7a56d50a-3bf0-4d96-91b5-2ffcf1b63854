﻿
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TajneedApp.Models;

public partial class Airbase
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
   
    public int AirbaseId { get; set; }
     [Display(Name = "اسم القاعدة ")]
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public string? AirbaseName { get; set; }

}
