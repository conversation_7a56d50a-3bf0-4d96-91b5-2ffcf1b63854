@model TajneedApp.Models.Exam

@{
    ViewData["Title"] = "حذف امتحان";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>تأكيد حذف الامتحان
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-warning me-2"></i>
                        <strong>تحذير:</strong> هل أنت متأكد من رغبتك في حذف هذا الامتحان؟ هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card border-0 bg-light">
                                <div class="card-body">
                                    <h5 class="card-title text-danger mb-4">
                                        <i class="fas fa-file-alt me-2"></i>تفاصيل الامتحان المراد حذفه
                                    </h5>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold text-primary">
                                                    <i class="fas fa-file-alt me-1"></i>@Html.DisplayNameFor(model => model.ExamName)
                                                </label>
                                                <p class="form-control-plaintext fw-bold">@Model.ExamName</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold text-primary">
                                                    <i class="fas fa-tags me-1"></i>@Html.DisplayNameFor(model => model.Category)
                                                </label>
                                                <p class="form-control-plaintext">
                                                    <span class="badge bg-info">@(Model.Category?.CategoryName ?? "غير محدد")</span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold text-primary">
                                                    <i class="fas fa-chart-line me-1"></i>@Html.DisplayNameFor(model => model.EvaluationType)
                                                </label>
                                                <p class="form-control-plaintext">
                                                    <span class="badge bg-secondary">@Model.EvaluationTypeDisplayName</span>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold text-primary">
                                                    <i class="fas fa-check-circle me-1"></i>@Html.DisplayNameFor(model => model.PassingScore)
                                                </label>
                                                <p class="form-control-plaintext">
                                                    @if (Model.PassingScore.HasValue)
                                                    {
                                                        <span class="text-success fw-bold">@Model.PassingScore</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">غير محدد</span>
                                                    }
                                                </p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label fw-bold text-primary">
                                                    <i class="fas fa-star me-1"></i>@Html.DisplayNameFor(model => model.MaxScore)
                                                </label>
                                                <p class="form-control-plaintext">
                                                    @if (Model.MaxScore.HasValue)
                                                    {
                                                        <span class="text-warning fw-bold">@Model.MaxScore</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">غير محدد</span>
                                                    }
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>معلومات مهمة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-check text-danger me-2"></i>
                                            سيتم حذف الامتحان نهائياً
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-danger me-2"></i>
                                            قد تتأثر نتائج المرشحين المرتبطة
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-danger me-2"></i>
                                            لا يمكن التراجع عن هذا الإجراء
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <form asp-action="Delete" method="post">
                                <input type="hidden" asp-for="ExamId" />
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من رغبتك في حذف هذا الامتحان؟')">
                                        <i class="fas fa-trash me-2"></i>تأكيد الحذف
                                    </button>
                                    <a asp-action="Details" asp-route-id="@Model.ExamId" class="btn btn-info">
                                        <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                    </a>
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>إلغاء والعودة
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
