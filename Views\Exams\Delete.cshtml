@model TajneedApp.Models.Exam

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Exam</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.ExamName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.ExamName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.EvaluationType)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EvaluationType)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.PassingScore)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.PassingScore)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.MaxScore)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.MaxScore)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Category)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Category.CategoryCode)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="ExamId" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
