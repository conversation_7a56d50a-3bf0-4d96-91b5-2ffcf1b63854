﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;



namespace TajneedApp.Models;

public partial class Candidate
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    [Display(Name = "معرف المرشح")]
    public int CandidateId { get; set; }

    [Display(Name = "الاسم الكامل")]
    [Required(ErrorMessage = "حقل الاسم الكامل مطلوب.")]
    [StringLength(200, ErrorMessage = "الاسم الكامل يجب أن لا يتجاوز 200 حرف.")]
    public string? FullName { get; set; }

    [Display(Name = "فئة المرشح")]
    [Required(ErrorMessage = "حقل فئة المرشح مطلوب.")]
    public int? CategoryId { get; set; }

    [Display(Name = "الرقم العسكري")]
    [Required(ErrorMessage = "حقل الرقم العسكري مطلوب.")]
    public string? ServiceNumber { get; set; }

    [Display(Name = "رقم الهوية الوطنية")]
 
    public string? NationalIdNumber { get; set; }

    [Display(Name = "الرتبة")]
 
    public int? RankId { get; set; }

    [Display(Name = "القاعدة الجوية")]
  
    public int? AirbaseId { get; set; }

    [Display(Name = "القسم/الإدارة")]
 
    public string? Department { get; set; }

    [Display(Name = "رقم الهاتف الأساسي")]
    [Required(ErrorMessage = "حقل رقم الهاتف الأساسي مطلوب.")]
    [Phone(ErrorMessage = "الرجاء إدخال رقم هاتف صحيح.")]
    public string? Phone1 { get; set; }

    [Display(Name = "رقم الهاتف الثانوي")]
   
    public string? Phone2 { get; set; }

    [Display(Name = "رقم هاتف إضافي")]
  
    public string? Phone3 { get; set; }

    [Display(Name = "التخصص")]
    [Required(ErrorMessage = "حقل التخصص مطلوب.")]
    public string? Major { get; set; }

    [Display(Name = "الجامعة")]
    [Required(ErrorMessage = "حقل الجامعة مطلوب.")]
    public string? University { get; set; }

    [Display(Name = "سنة التخرج")]
    [Required(ErrorMessage = "حقل سنة التخرج مطلوب.")]
    public int? GraduationYear { get; set; }

    [Display(Name = "التقدير/المعدل")]
    [Required(ErrorMessage = "حقل التقدير/المعدل مطلوب.")]
    public string? MarksGrade { get; set; }

    [Display(Name = "الحالة (نشط)")]
    public long? IsActive { get; set; } // Typically not required from user input directly

    [Display(Name = "العنوان")]
    [Required(ErrorMessage = "حقل العنوان مطلوب.")]
    public string? Address { get; set; }

    [Display(Name = "تاريخ الميلاد")]
    [Required(ErrorMessage = "حقل تاريخ الميلاد مطلوب.")]
    [DataType(DataType.Date)]
    [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
    public DateTime? DateOfBirth { get; set; }

    [Display(Name = "المسمى الوظيفي")]
    [Required(ErrorMessage = "حقل المسمى الوظيفي مطلوب.")]
    public string? JobTitle { get; set; }
  
    [Display(Name = "القاعدة الجوية")]
    public virtual Airbase? Airbase { get; set; }
    [Display(Name = "فئة المرشح")]
    public virtual Category? Category { get; set; }

    [Display(Name ="الرتبة")]
    public virtual Rank? Rank { get; set; }
}
