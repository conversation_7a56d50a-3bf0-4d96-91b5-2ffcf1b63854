using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;


namespace TajneedApp.Models;

public partial class CandidateExamResult
{

    [Key]
    public int CandidateExamResultId { get; set; }

    [Display(Name = "رقم المرشح")]
    [Required(ErrorMessage = "حقل رقم المرشح مطلوب.")]
    public int? CandidateId { get; set; }

    [Display(Name = "رقم الامتحان")]
    [Required(ErrorMessage = "حقل رقم الامتحان مطلوب.")]
    public int? ExamId { get; set; }

    [Display(Name = "الدرجة")]
    [Required(ErrorMessage = "حقل الدرجة مطلوب.")]
    public int? Score { get; set; }
    [Display(Name = "النتيجة النصية")]
    [Required(ErrorMessage = "حقل الدرجة مطلوب.")]
    public int? TextResult { get; set; }

    [Display(Name = "تاريخ الامتحان")]
    [Required(ErrorMessage = "حقل تاريخ الامتحان مطلوب.")]
    [DataType(DataType.Date)]
    [DisplayFormat(DataFormatString = "{0:yyyy-MM-dd}", ApplyFormatInEditMode = true)]
    public DateTime? ExamDate { get; set; }

    [Display(Name = "تاريخ الإنشاء")]
    public DateTime? CreatedDate { get; set; } = DateTime.UtcNow;

    [Display(Name = "المستخدم الذي قام بإنشاء السجل")]
    public string? CreatedBy { get; set; } 

[Display(Name = "تاريخ آخر تعديل")]
public DateTime? LastModifiedDate { get; set; } = DateTime.UtcNow;

    [Display(Name = "المستخدم الذي قام بتعديل السجل")]
    public string? LastModifiedBy { get; set; }
  
    [ValidateNever]
    public virtual Candidate? Candidate { get; set; }

    [ValidateNever]
    public virtual Exam? Exam { get; set; }
}
