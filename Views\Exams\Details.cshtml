@model TajneedApp.Models.Exam

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Exam</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.ExamName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.ExamName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.EvaluationType)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.EvaluationType)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.PassingScore)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.PassingScore)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.MaxScore)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.MaxScore)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Category)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.Category.CategoryName)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.ExamId">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
