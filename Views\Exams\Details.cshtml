@model TajneedApp.Models.Exam

@{
    ViewData["Title"] = "تفاصيل الامتحان";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>تفاصيل الامتحان: @Model.ExamName
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card border-0 bg-light h-100">
                                        <div class="card-body">
                                            <h6 class="card-title text-primary">
                                                <i class="fas fa-file-alt me-2"></i>@Html.DisplayNameFor(model => model.ExamName)
                                            </h6>
                                            <p class="card-text fw-bold fs-5">@Model.ExamName</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-0 bg-light h-100">
                                        <div class="card-body">
                                            <h6 class="card-title text-primary">
                                                <i class="fas fa-tags me-2"></i>@Html.DisplayNameFor(model => model.Category)
                                            </h6>
                                            <p class="card-text">
                                                <span class="badge bg-info fs-6">@(Model.Category?.CategoryName ?? "غير محدد")</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="card border-0 bg-light h-100">
                                        <div class="card-body text-center">
                                            <h6 class="card-title text-primary">
                                                <i class="fas fa-chart-line me-2"></i>@Html.DisplayNameFor(model => model.EvaluationType)
                                            </h6>
                                            <p class="card-text">
                                                <span class="badge bg-secondary fs-6">@Model.EvaluationTypeDisplayName</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-0 bg-light h-100">
                                        <div class="card-body text-center">
                                            <h6 class="card-title text-success">
                                                <i class="fas fa-check-circle me-2"></i>@Html.DisplayNameFor(model => model.PassingScore)
                                            </h6>
                                            <p class="card-text">
                                                @if (Model.PassingScore.HasValue)
                                                {
                                                    <span class="fs-4 fw-bold text-success">@Model.PassingScore</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-0 bg-light h-100">
                                        <div class="card-body text-center">
                                            <h6 class="card-title text-warning">
                                                <i class="fas fa-star me-2"></i>@Html.DisplayNameFor(model => model.MaxScore)
                                            </h6>
                                            <p class="card-text">
                                                @if (Model.MaxScore.HasValue)
                                                {
                                                    <span class="fs-4 fw-bold text-warning">@Model.MaxScore</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-cogs me-2"></i>الإجراءات المتاحة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <a asp-action="Edit" asp-route-id="@Model.ExamId" class="btn btn-warning">
                                            <i class="fas fa-edit me-2"></i>تعديل الامتحان
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@Model.ExamId" class="btn btn-danger">
                                            <i class="fas fa-trash me-2"></i>حذف الامتحان
                                        </a>
                                        <hr>
                                        <a asp-action="Index" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                                        </a>
                                        <a asp-action="Create" class="btn btn-success">
                                            <i class="fas fa-plus me-2"></i>إضافة امتحان جديد
                                        </a>
                                    </div>
                                </div>
                            </div>

                            @if (Model.EvaluationType == TajneedApp.Models.EvaluationType.Mark)
                            {
                                <div class="card border-info mt-3">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-calculator me-2"></i>معلومات الدرجات
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        @if (Model.PassingScore.HasValue && Model.MaxScore.HasValue)
                                        {
                                            var percentage = Math.Round((double)Model.PassingScore.Value / Model.MaxScore.Value * 100, 1);
                                            <p class="mb-2">
                                                <strong>النسبة المطلوبة للنجاح:</strong>
                                                <span class="text-primary">@percentage%</span>
                                            </p>
                                            <div class="progress mb-2">
                                                <div class="progress-bar bg-success" role="progressbar"
                                                     style="width: @percentage%"
                                                     aria-valuenow="@percentage" aria-valuemin="0" aria-valuemax="100">
                                                    @percentage%
                                                </div>
                                            </div>
                                        }
                                        <small class="text-muted">
                                            يجب على المرشح الحصول على @Model.PassingScore درجة أو أكثر من أصل @Model.MaxScore درجة للنجاح
                                        </small>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
