using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace TajneedApp.Models;

public partial class EvaluationPath
{
    [Key]
    public int EvaluationPathId { get; set; }

    [Display(Name = "اسم مسار التقييم")]
    [Required(ErrorMessage = "حقل اسم مسار التقييم مطلوب.")]
    [StringLength(200, ErrorMessage = "اسم مسار التقييم يجب أن لا يتجاوز 200 حرف.")]
    public string? PathName { get; set; }

    [Display(Name = "وصف مسار التقييم")]
    [Required(ErrorMessage = "حقل وصف مسار التقييم مطلوب.")]
    [StringLength(1000, ErrorMessage = "وصف مسار التقييم يجب أن لا يتجاوز 1000 حرف.")]
    public string? Description { get; set; }

    [Display(Name = "معرف الفئة")]
    [Required(ErrorMessage = "حقل معرف الفئة مطلوب.")]
    public int CategoryId { get; set; }

    [Display(Name = "نشط")]
    public bool IsActive { get; set; } = true;

    [Display(Name = "تاريخ الإنشاء")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    [Display(Name = "المستخدم المنشئ")]
    public string? CreatedBy { get; set; }

    [Display(Name = "تاريخ آخر تعديل")]
    public DateTime? LastModifiedDate { get; set; }

    [Display(Name = "المستخدم المعدل")]
    public string? LastModifiedBy { get; set; }

    // Navigation Properties
    [ValidateNever]
    [Display(Name = "الفئة")]
    public virtual Category? Category { get; set; }

    [ValidateNever]
    public virtual ICollection<EvaluationComponent> Components { get; set; } = new List<EvaluationComponent>();

    [ValidateNever]
    public virtual ICollection<CandidateEvaluationProgress> CandidateProgress { get; set; } = new List<CandidateEvaluationProgress>();
}
