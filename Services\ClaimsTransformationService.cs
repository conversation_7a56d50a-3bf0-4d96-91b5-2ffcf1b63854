using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using TajneedApp.Data;
using TajneedApp.Models;

namespace TajneedApp.Services
{
    public class ClaimsTransformationService : IClaimsTransformation
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;

        public ClaimsTransformationService(UserManager<ApplicationUser> userManager, ApplicationDbContext context)
        {
            _userManager = userManager;
            _context = context;
        }

        public async Task<ClaimsPrincipal> TransformAsync(ClaimsPrincipal principal)
        {
            // Only transform if user is authenticated
            if (!principal.Identity?.IsAuthenticated ?? true)
                return principal;

            var claimsIdentity = (ClaimsIdentity)principal.Identity;

            // Check if we already have the custom claims to avoid duplicate processing
            if (claimsIdentity.HasClaim("IsActive", "True") || claimsIdentity.HasClaim("IsActive", "False"))
                return principal;

            // Get the user ID from the claims
            var userId = principal.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
                return principal;

            try
            {
                // Get the user with related data
                var user = await _context.Users
                    .Include(u => u.Rank)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                    return principal;

                // Add custom claims
                var claims = new List<Claim>();

                // Add IsActive claim
                claims.Add(new Claim("IsActive", user.IsActive.ToString()));

                // Add FullName claim if not already present
                if (!claimsIdentity.HasClaim("FullName", user.FullName ?? ""))
                {
                    claims.Add(new Claim("FullName", user.FullName ?? ""));
                }

                // Add ServiceNumber claim if not already present
                if (!claimsIdentity.HasClaim("ServiceNumber", user.ServiceNumber ?? ""))
                {
                    claims.Add(new Claim("ServiceNumber", user.ServiceNumber ?? ""));
                }

                // Add Rank claim if not already present
                if (user.Rank != null && !claimsIdentity.HasClaim("Rank", user.Rank.RankName))
                {
                    claims.Add(new Claim("Rank", user.Rank.RankName));
                }

                // Add Position claim if available
                if (!string.IsNullOrEmpty(user.Position) && !claimsIdentity.HasClaim("Position", user.Position))
                {
                    claims.Add(new Claim("Position", user.Position));
                }

                // Add committee-related claims
                if (user.IsCommitteeMember)
                {
                    claims.Add(new Claim("IsCommitteeMember", "True"));
                }

                if (user.IsCommitteeChair)
                {
                    claims.Add(new Claim("IsCommitteeChair", "True"));
                }

                // Add RankId claim for potential use
                claims.Add(new Claim("RankId", user.RankId.ToString()));

                // Add CreatedAt claim
                claims.Add(new Claim("CreatedAt", user.CreatedAt.ToString("yyyy-MM-dd")));

                // Add all new claims to the identity
                claimsIdentity.AddClaims(claims);
            }
            catch (Exception)
            {
                // Log the exception if needed, but don't break the authentication flow
                // In production, you might want to log this error
            }

            return principal;
        }
    }
}
