using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using TajneedApp.Data;
using TajneedApp.Models;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace TajneedApp.Controllers
{
    [Authorize]
    public class EvaluationComponentsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public EvaluationComponentsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: EvaluationComponents
        public async Task<IActionResult> Index(int? pathId)
        {
            var query = _context.EvaluationComponents
                .Include(e => e.EvaluationPath)
                    .ThenInclude(p => p.Category)
                .Include(e => e.Criteria)
                .AsQueryable();

            if (pathId.HasValue)
            {
                query = query.Where(e => e.EvaluationPathId == pathId.Value);
                var path = await _context.EvaluationPaths
                    .Include(p => p.Category)
                    .FirstOrDefaultAsync(p => p.EvaluationPathId == pathId.Value);
                ViewData["EvaluationPath"] = path;
            }

            var components = await query
                .OrderBy(e => e.EvaluationPath.Category.CategoryName)
                .ThenBy(e => e.EvaluationPath.PathName)
                .ThenBy(e => e.DisplayOrder)
                .ToListAsync();

            return View(components);
        }

        // GET: EvaluationComponents/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var evaluationComponent = await _context.EvaluationComponents
                .Include(e => e.EvaluationPath)
                    .ThenInclude(p => p.Category)
                .Include(e => e.Criteria.OrderBy(c => c.DisplayOrder))
                .FirstOrDefaultAsync(m => m.ComponentId == id);

            if (evaluationComponent == null)
            {
                return NotFound();
            }

            return View(evaluationComponent);
        }

        // GET: EvaluationComponents/Create
        public async Task<IActionResult> Create(int? pathId)
        {
            var evaluationPaths = await _context.EvaluationPaths
                .Include(p => p.Category)
                .Where(p => p.IsActive)
                .ToListAsync();

            ViewData["EvaluationPathId"] = new SelectList(
                evaluationPaths.Select(p => new { 
                    p.EvaluationPathId, 
                    DisplayText = $"{p.Category?.CategoryName} - {p.PathName}" 
                }), 
                "EvaluationPathId", 
                "DisplayText", 
                pathId);

            ViewData["ComponentType"] = GetComponentTypeSelectList();
            ViewData["EvaluationType"] = GetEvaluationTypeSelectList();

            var component = new EvaluationComponent();
            if (pathId.HasValue)
            {
                component.EvaluationPathId = pathId.Value;
                // Set next display order
                var maxOrder = await _context.EvaluationComponents
                    .Where(c => c.EvaluationPathId == pathId.Value)
                    .MaxAsync(c => (int?)c.DisplayOrder) ?? 0;
                component.DisplayOrder = maxOrder + 1;
            }

            return View(component);
        }

        // POST: EvaluationComponents/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("ComponentId,ComponentName,ComponentDescription,EvaluationPathId,ComponentType,EvaluationType,WeightPercentage,MaxScore,PassingScore,DisplayOrder,IsRequired,IsActive")] EvaluationComponent evaluationComponent)
        {
            if (ModelState.IsValid)
            {
                evaluationComponent.CreatedDate = DateTime.UtcNow;
                evaluationComponent.CreatedBy = User.FindFirstValue(ClaimTypes.NameIdentifier);

                _context.Add(evaluationComponent);
                await _context.SaveChangesAsync();

                TempData["Success"] = "تم إنشاء مكون التقييم بنجاح";
                return RedirectToAction(nameof(Details), new { id = evaluationComponent.ComponentId });
            }

            await PopulateViewData(evaluationComponent);
            return View(evaluationComponent);
        }

        // GET: EvaluationComponents/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var evaluationComponent = await _context.EvaluationComponents.FindAsync(id);
            if (evaluationComponent == null)
            {
                return NotFound();
            }

            await PopulateViewData(evaluationComponent);
            return View(evaluationComponent);
        }

        // POST: EvaluationComponents/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("ComponentId,ComponentName,ComponentDescription,EvaluationPathId,ComponentType,EvaluationType,WeightPercentage,MaxScore,PassingScore,DisplayOrder,IsRequired,IsActive,CreatedDate,CreatedBy")] EvaluationComponent evaluationComponent)
        {
            if (id != evaluationComponent.ComponentId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(evaluationComponent);
                    await _context.SaveChangesAsync();

                    TempData["Success"] = "تم تحديث مكون التقييم بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!EvaluationComponentExists(evaluationComponent.ComponentId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Details), new { id = evaluationComponent.ComponentId });
            }

            await PopulateViewData(evaluationComponent);
            return View(evaluationComponent);
        }

        // GET: EvaluationComponents/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var evaluationComponent = await _context.EvaluationComponents
                .Include(e => e.EvaluationPath)
                    .ThenInclude(p => p.Category)
                .Include(e => e.Criteria)
                .FirstOrDefaultAsync(m => m.ComponentId == id);

            if (evaluationComponent == null)
            {
                return NotFound();
            }

            return View(evaluationComponent);
        }

        // POST: EvaluationComponents/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var evaluationComponent = await _context.EvaluationComponents.FindAsync(id);
            if (evaluationComponent != null)
            {
                _context.EvaluationComponents.Remove(evaluationComponent);
                await _context.SaveChangesAsync();
                TempData["Success"] = "تم حذف مكون التقييم بنجاح";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool EvaluationComponentExists(int id)
        {
            return _context.EvaluationComponents.Any(e => e.ComponentId == id);
        }

        private async Task PopulateViewData(EvaluationComponent component)
        {
            var evaluationPaths = await _context.EvaluationPaths
                .Include(p => p.Category)
                .Where(p => p.IsActive)
                .ToListAsync();

            ViewData["EvaluationPathId"] = new SelectList(
                evaluationPaths.Select(p => new { 
                    p.EvaluationPathId, 
                    DisplayText = $"{p.Category?.CategoryName} - {p.PathName}" 
                }), 
                "EvaluationPathId", 
                "DisplayText", 
                component.EvaluationPathId);

            ViewData["ComponentType"] = GetComponentTypeSelectList(component.ComponentType);
            ViewData["EvaluationType"] = GetEvaluationTypeSelectList(component.EvaluationType);
        }

        private static SelectList GetComponentTypeSelectList(ComponentType? selectedValue = null)
        {
            var items = Enum.GetValues(typeof(ComponentType))
                .Cast<ComponentType>()
                .Select(e => new
                {
                    Value = e,
                    Text = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<DisplayAttribute>()
                        ?.Name ?? e.ToString()
                });

            return new SelectList(items, "Value", "Text", selectedValue);
        }

        private static SelectList GetEvaluationTypeSelectList(EvaluationType? selectedValue = null)
        {
            var items = Enum.GetValues(typeof(EvaluationType))
                .Cast<EvaluationType>()
                .Select(e => new
                {
                    Value = e,
                    Text = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<DisplayAttribute>()
                        ?.Name ?? e.ToString()
                });

            return new SelectList(items, "Value", "Text", selectedValue);
        }

        // AJAX: Get components for a path
        [HttpGet]
        public async Task<IActionResult> GetComponentsForPath(int pathId)
        {
            var components = await _context.EvaluationComponents
                .Where(c => c.EvaluationPathId == pathId && c.IsActive)
                .OrderBy(c => c.DisplayOrder)
                .Select(c => new { c.ComponentId, c.ComponentName })
                .ToListAsync();

            return Json(components);
        }

        // POST: Reorder components
        [HttpPost]
        public async Task<IActionResult> ReorderComponents([FromBody] List<int> componentIds)
        {
            try
            {
                for (int i = 0; i < componentIds.Count; i++)
                {
                    var component = await _context.EvaluationComponents.FindAsync(componentIds[i]);
                    if (component != null)
                    {
                        component.DisplayOrder = i + 1;
                    }
                }

                await _context.SaveChangesAsync();
                return Json(new { success = true, message = "تم تحديث ترتيب المكونات بنجاح" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "حدث خطأ أثناء تحديث الترتيب" });
            }
        }
    }
}
