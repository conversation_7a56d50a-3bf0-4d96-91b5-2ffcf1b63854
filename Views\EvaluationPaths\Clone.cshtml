@model TajneedApp.Models.EvaluationPath

@{
    ViewData["Title"] = "نسخ مسار التقييم";
    var originalPath = ViewData["OriginalPath"] as TajneedApp.Models.EvaluationPath;
}

<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h3 class="m-0 font-weight-bold">
                <i class="fas fa-copy me-2"></i>
                نسخ مسار التقييم
            </h3>
        </div>
        <div class="card-body">
            @if (originalPath != null)
            {
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>المسار المراد نسخه:</h6>
                    <p class="mb-2">
                        <strong>@originalPath.PathName</strong> - 
                        <span class="badge bg-info">@originalPath.Category?.CategoryName</span>
                    </p>
                    <p class="mb-0">
                        <small>
                            سيتم نسخ جميع المكونات (@originalPath.Components.Count مكون) والمعايير المرتبطة بها.
                        </small>
                    </p>
                </div>
            }

            <form asp-action="Clone" method="post">
                <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                
                <input type="hidden" name="originalId" value="@originalPath?.EvaluationPathId" />
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="PathName" class="form-label"></label>
                            <input asp-for="PathName" class="form-control" placeholder="اسم المسار الجديد" />
                            <span asp-validation-for="PathName" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="CategoryId" class="form-label"></label>
                            <select asp-for="CategoryId" class="form-control" asp-items="ViewBag.CategoryId">
                                <option value="">اختر الفئة للمسار الجديد</option>
                            </select>
                            <span asp-validation-for="CategoryId" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Description" class="form-label"></label>
                    <textarea asp-for="Description" class="form-control" rows="4" placeholder="وصف المسار الجديد"></textarea>
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <div class="form-check">
                        <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                        <label asp-for="IsActive" class="form-check-label">
                            تفعيل المسار الجديد
                        </label>
                    </div>
                    <small class="form-text text-muted">يمكنك تفعيل المسار لاحقاً بعد مراجعة المكونات والمعايير</small>
                </div>

                @if (originalPath != null && originalPath.Components.Any())
                {
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>ما سيتم نسخه:</h6>
                        <ul class="mb-0">
                            @foreach (var component in originalPath.Components.OrderBy(c => c.DisplayOrder))
                            {
                                <li>
                                    <strong>@component.ComponentName</strong> 
                                    (@component.ComponentType.GetDisplayName()) - 
                                    الوزن: @component.WeightPercentage%
                                    @if (component.ComponentType == ComponentType.CommitteeEvaluation && component.Criteria.Any())
                                    {
                                        <span class="text-muted">(@component.Criteria.Count معيار)</span>
                                    }
                                </li>
                            }
                        </ul>
                    </div>
                }

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-copy"></i> نسخ المسار
                    </button>
                    <a asp-action="Details" asp-route-id="@originalPath?.EvaluationPathId" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للمسار الأصلي
                    </a>
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="fas fa-list"></i> العودة للقائمة
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
