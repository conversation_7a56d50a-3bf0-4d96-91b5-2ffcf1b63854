@model TajneedApp.Models.EvaluationPath

@{
    ViewData["Title"] = "إنشاء مسار تقييم جديد";
}

<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h3 class="m-0 font-weight-bold">إنشاء مسار تقييم جديد</h3>
        </div>
        <div class="card-body">
            <form asp-action="Create">
                <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="PathName" class="form-label"></label>
                            <input asp-for="PathName" class="form-control" placeholder="مثال: مسار تقييم الضباط" />
                            <span asp-validation-for="PathName" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="CategoryId" class="form-label"></label>
                            <select asp-for="CategoryId" class="form-control" asp-items="ViewBag.CategoryId">
                                <option value="">اختر الفئة</option>
                            </select>
                            <span asp-validation-for="CategoryId" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Description" class="form-label"></label>
                    <textarea asp-for="Description" class="form-control" rows="4" placeholder="وصف تفصيلي لمسار التقييم وأهدافه"></textarea>
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <div class="form-check">
                        <input asp-for="IsActive" class="form-check-input" type="checkbox" checked />
                        <label asp-for="IsActive" class="form-check-label">
                            تفعيل المسار
                        </label>
                    </div>
                    <small class="form-text text-muted">يمكن تفعيل أو إلغاء تفعيل المسار لاحقاً</small>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> بعد إنشاء المسار، ستتمكن من إضافة مكونات التقييم المختلفة مثل الامتحانات المكتوبة، الاختبارات العملية، وتقييمات اللجان.
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> إنشاء المسار
                    </button>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للقائمة
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
