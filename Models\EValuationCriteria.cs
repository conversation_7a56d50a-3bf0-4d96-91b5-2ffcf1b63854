using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace TajneedApp.Models;

public partial class EValuationCriteria
{

    [Key]
    public int EValuationCriteriaId { get; set; }
        [Display(Name = "المعيار")]
    [Required(ErrorMessage = "حقل المعيار مطلوب.")]
    public int? CriteriaName { get; set; }

    [Display(Name = "وصف المعيار")]
    [Required(ErrorMessage = "حقل المعيار مطلوب.")]
    public string? CriteriaDescription { get; set; }
    [Display(Name = "ترتيب العرض")]
    public string? DisplayOrder { get; set; }
    [Display(Name = "الدرجة القصوى")]
    public string? MaxScore { get; set; }

    }
