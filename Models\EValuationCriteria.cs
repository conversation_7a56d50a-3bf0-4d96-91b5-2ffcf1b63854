using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace TajneedApp.Models;

public partial class CommitteeEvaluationCriteria
{
    [Key]
    public int CriteriaId { get; set; }

    [Display(Name = "اسم المعيار")]
    [Required(ErrorMessage = "حقل اسم المعيار مطلوب.")]
    [StringLength(200, ErrorMessage = "اسم المعيار يجب أن لا يتجاوز 200 حرف.")]
    public string? CriteriaName { get; set; }

    [Display(Name = "وصف المعيار")]
    [Required(ErrorMessage = "حقل وصف المعيار مطلوب.")]
    [StringLength(500, ErrorMessage = "وصف المعيار يجب أن لا يتجاوز 500 حرف.")]
    public string? CriteriaDescription { get; set; }

    [Display(Name = "معرف الامتحان")]
    [Required(ErrorMessage = "حقل معرف الامتحان مطلوب.")]
    public int? ExamId { get; set; }

    [Display(Name = "الدرجة القصوى")]
    [Required(ErrorMessage = "حقل الدرجة القصوى مطلوب.")]
    [Range(1, 100, ErrorMessage = "الدرجة القصوى يجب أن تكون بين 1 و 100")]
    public decimal MaxScore { get; set; }

    [Display(Name = "نسبة الوزن (%)")]
    [Required(ErrorMessage = "حقل نسبة الوزن مطلوب.")]
    [Range(0.01, 100, ErrorMessage = "نسبة الوزن يجب أن تكون بين 0.01 و 100")]
    public decimal WeightPercentage { get; set; }

    [Display(Name = "ترتيب العرض")]
    [Required(ErrorMessage = "حقل ترتيب العرض مطلوب.")]
    public int DisplayOrder { get; set; }

    [Display(Name = "نشط")]
    public bool IsActive { get; set; } = true;

    [Display(Name = "تاريخ الإنشاء")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    [Display(Name = "المستخدم المنشئ")]
    public string? CreatedBy { get; set; }

    // Navigation Properties
    [ValidateNever]
    [Display(Name = "الامتحان")]
    public virtual Exam? Exam { get; set; }

    [ValidateNever]
    public virtual ICollection<CandidateCommitteeEvaluation> Evaluations { get; set; } = new List<CandidateCommitteeEvaluation>();
}
