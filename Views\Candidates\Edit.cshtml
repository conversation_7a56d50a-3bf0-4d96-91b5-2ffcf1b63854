@model TajneedApp.Models.Candidate

@{
    ViewData["Title"] = "تعديل بيانات المرشح";
}

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="text-primary fw-bold mb-1">
                        <i class="fas fa-user-edit me-2"></i>تعديل بيانات المرشح
                    </h2>
                    <p class="text-muted mb-0">تحديث وتعديل معلومات المرشح في النظام</p>
                </div>
                <div>
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Form Card -->
    <div class="card shadow border-0">
        <div class="card-header bg-primary text-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>نموذج تعديل البيانات
                    </h5>
                </div>
                <div class="col-auto">
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-id-badge me-1"></i>@Model.ServiceNumber
                    </span>
                </div>
            </div>
        </div>

        <div class="card-body p-4">
            <form asp-action="Edit" id="editCandidateForm">
                <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                <input type="hidden" asp-for="CandidateId" />

                <!-- Personal Information Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary fw-bold border-bottom pb-2 mb-3">
                            <i class="fas fa-user me-2"></i>المعلومات الشخصية
                        </h6>
                    </div>
                </div>

                <div class="row">
                    <!-- Full Name -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="FullName" class="form-label fw-bold">
                            <i class="fas fa-user text-primary me-2"></i>الاسم الكامل
                        </label>
                        <input asp-for="FullName" class="form-control form-control-lg" placeholder="أدخل الاسم الكامل" />
                        <span asp-validation-for="FullName" class="text-danger small"></span>
                    </div>

                    <!-- Service Number -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="ServiceNumber" class="form-label fw-bold">
                            <i class="fas fa-id-badge text-primary me-2"></i>الرقم العسكري
                        </label>
                        <input asp-for="ServiceNumber" class="form-control form-control-lg" placeholder="أدخل الرقم العسكري" />
                        <span asp-validation-for="ServiceNumber" class="text-danger small"></span>
                    </div>

                    <!-- National ID -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="NationalIdNumber" class="form-label fw-bold">
                            <i class="fas fa-id-card text-primary me-2"></i>رقم الهوية الوطنية
                        </label>
                        <input asp-for="NationalIdNumber" class="form-control form-control-lg" placeholder="أدخل رقم الهوية" />
                        <span asp-validation-for="NationalIdNumber" class="text-danger small"></span>
                    </div>

                    <!-- Date of Birth -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="DateOfBirth" class="form-label fw-bold">
                            <i class="fas fa-calendar text-primary me-2"></i>تاريخ الميلاد
                        </label>
                        <input asp-for="DateOfBirth" type="date" class="form-control form-control-lg" />
                        <span asp-validation-for="DateOfBirth" class="text-danger small"></span>
                    </div>
                </div>

                <!-- Military Information Section -->
                <div class="row mb-4 mt-4">
                    <div class="col-12">
                        <h6 class="text-success fw-bold border-bottom pb-2 mb-3">
                            <i class="fas fa-star me-2"></i>المعلومات العسكرية
                        </h6>
                    </div>
                </div>

                <div class="row">
                    <!-- Rank -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="RankId" class="form-label fw-bold">
                            <i class="fas fa-star text-success me-2"></i>الرتبة
                        </label>
                        <select asp-for="RankId" class="form-select form-select-lg" asp-items="ViewBag.RankId">
                            <option value="">اختر الرتبة</option>
                        </select>
                        <span asp-validation-for="RankId" class="text-danger small"></span>
                    </div>

                    <!-- Airbase -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="AirbaseId" class="form-label fw-bold">
                            <i class="fas fa-plane text-success me-2"></i>القاعدة الجوية
                        </label>
                        <select asp-for="AirbaseId" class="form-select form-select-lg" asp-items="ViewBag.AirbaseId">
                            <option value="">اختر القاعدة الجوية</option>
                        </select>
                        <span asp-validation-for="AirbaseId" class="text-danger small"></span>
                    </div>

                    <!-- Department -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="Department" class="form-label fw-bold">
                            <i class="fas fa-building text-success me-2"></i>القسم
                        </label>
                        <input asp-for="Department" class="form-control form-control-lg" placeholder="أدخل اسم القسم" />
                        <span asp-validation-for="Department" class="text-danger small"></span>
                    </div>

                    <!-- Job Title -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="JobTitle" class="form-label fw-bold">
                            <i class="fas fa-briefcase text-success me-2"></i>المسمى الوظيفي
                        </label>
                        <input asp-for="JobTitle" class="form-control form-control-lg" placeholder="أدخل المسمى الوظيفي" />
                        <span asp-validation-for="JobTitle" class="text-danger small"></span>
                    </div>

                    <!-- Category -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="CategoryId" class="form-label fw-bold">
                            <i class="fas fa-tags text-success me-2"></i>الفئة
                        </label>
                        <select asp-for="CategoryId" class="form-select form-select-lg" asp-items="ViewBag.CategoryId">
                            <option value="">اختر الفئة</option>
                        </select>
                        <span asp-validation-for="CategoryId" class="text-danger small"></span>
                    </div>

                    <!-- Is Active -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="IsActive" class="form-label fw-bold">
                            <i class="fas fa-toggle-on text-success me-2"></i>الحالة
                        </label>
                        <select asp-for="IsActive" class="form-select form-select-lg">
                            <option value="">اختر الحالة</option>
                            <option value="1">نشط</option>
                            <option value="0">غير نشط</option>
                        </select>
                        <span asp-validation-for="IsActive" class="text-danger small"></span>
                    </div>
                </div>

                <!-- Contact Information Section -->
                <div class="row mb-4 mt-4">
                    <div class="col-12">
                        <h6 class="text-info fw-bold border-bottom pb-2 mb-3">
                            <i class="fas fa-phone me-2"></i>معلومات الاتصال
                        </h6>
                    </div>
                </div>

                <div class="row">
                    <!-- Phone 1 -->
                    <div class="col-md-4 mb-3">
                        <label asp-for="Phone1" class="form-label fw-bold">
                            <i class="fas fa-phone text-info me-2"></i>الهاتف الأول
                        </label>
                        <input asp-for="Phone1" type="tel" class="form-control form-control-lg" placeholder="05xxxxxxxx" />
                        <span asp-validation-for="Phone1" class="text-danger small"></span>
                    </div>

                    <!-- Phone 2 -->
                    <div class="col-md-4 mb-3">
                        <label asp-for="Phone2" class="form-label fw-bold">
                            <i class="fas fa-phone text-info me-2"></i>الهاتف الثاني
                        </label>
                        <input asp-for="Phone2" type="tel" class="form-control form-control-lg" placeholder="05xxxxxxxx" />
                        <span asp-validation-for="Phone2" class="text-danger small"></span>
                    </div>

                    <!-- Phone 3 -->
                    <div class="col-md-4 mb-3">
                        <label asp-for="Phone3" class="form-label fw-bold">
                            <i class="fas fa-phone text-info me-2"></i>الهاتف الثالث
                        </label>
                        <input asp-for="Phone3" type="tel" class="form-control form-control-lg" placeholder="05xxxxxxxx" />
                        <span asp-validation-for="Phone3" class="text-danger small"></span>
                    </div>

                    <!-- Address -->
                    <div class="col-12 mb-3">
                        <label asp-for="Address" class="form-label fw-bold">
                            <i class="fas fa-map-marker-alt text-info me-2"></i>العنوان
                        </label>
                        <textarea asp-for="Address" class="form-control" rows="3" placeholder="أدخل العنوان التفصيلي"></textarea>
                        <span asp-validation-for="Address" class="text-danger small"></span>
                    </div>
                </div>

                <!-- Educational Information Section -->
                <div class="row mb-4 mt-4">
                    <div class="col-12">
                        <h6 class="text-warning fw-bold border-bottom pb-2 mb-3">
                            <i class="fas fa-graduation-cap me-2"></i>المعلومات التعليمية
                        </h6>
                    </div>
                </div>

                <div class="row">
                    <!-- Major -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="Major" class="form-label fw-bold">
                            <i class="fas fa-book text-warning me-2"></i>التخصص
                        </label>
                        <input asp-for="Major" class="form-control form-control-lg" placeholder="أدخل التخصص" />
                        <span asp-validation-for="Major" class="text-danger small"></span>
                    </div>

                    <!-- University -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="University" class="form-label fw-bold">
                            <i class="fas fa-university text-warning me-2"></i>الجامعة
                        </label>
                        <input asp-for="University" class="form-control form-control-lg" placeholder="أدخل اسم الجامعة" />
                        <span asp-validation-for="University" class="text-danger small"></span>
                    </div>

                    <!-- Graduation Year -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="GraduationYear" class="form-label fw-bold">
                            <i class="fas fa-calendar-alt text-warning me-2"></i>سنة التخرج
                        </label>
                        <input asp-for="GraduationYear" type="number" min="1980" max="2030" class="form-control form-control-lg" placeholder="2024" />
                        <span asp-validation-for="GraduationYear" class="text-danger small"></span>
                    </div>

                    <!-- Marks Grade -->
                    <div class="col-md-6 mb-3">
                        <label asp-for="MarksGrade" class="form-label fw-bold">
                            <i class="fas fa-star text-warning me-2"></i>التقدير
                        </label>
                        <input asp-for="MarksGrade" type="number" step="0.01" min="0" max="5" class="form-control form-control-lg" placeholder="4.50" />
                        <span asp-validation-for="MarksGrade" class="text-danger small"></span>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="row mt-5">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <a asp-action="Index" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                                <a asp-action="Details" asp-route-id="@Model.CandidateId" class="btn btn-outline-info btn-lg ms-2">
                                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                </a>
                            </div>
                            <div>
                                <button type="submit" class="btn btn-success btn-lg px-5">
                                    <i class="fas fa-save me-2"></i>حفظ التغييرات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Form validation enhancement
            $('#editCandidateForm').on('submit', function(e) {
                var isValid = true;
                var firstErrorField = null;

                // Check required fields
                $(this).find('input[required], select[required]').each(function() {
                    if (!$(this).val()) {
                        isValid = false;
                        $(this).addClass('is-invalid');
                        if (!firstErrorField) {
                            firstErrorField = $(this);
                        }
                    } else {
                        $(this).removeClass('is-invalid').addClass('is-valid');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    if (firstErrorField) {
                        firstErrorField.focus();
                        $('html, body').animate({
                            scrollTop: firstErrorField.offset().top - 100
                        }, 500);
                    }

                    // Show error message
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ في البيانات',
                        text: 'يرجى التأكد من ملء جميع الحقول المطلوبة',
                        confirmButtonText: 'موافق'
                    });
                }
            });

            // Real-time validation
            $('input, select, textarea').on('blur', function() {
                if ($(this).attr('required') && !$(this).val()) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                }
            });

            // Phone number formatting
            $('input[type="tel"]').on('input', function() {
                var value = $(this).val().replace(/\D/g, '');
                if (value.length > 0) {
                    if (value.length <= 10) {
                        $(this).val(value);
                    } else {
                        $(this).val(value.substring(0, 10));
                    }
                }
            });

            // Success message on form submission
            $('#editCandidateForm').on('submit', function() {
                if ($(this).valid()) {
                    Swal.fire({
                        icon: 'success',
                        title: 'جاري الحفظ...',
                        text: 'يتم حفظ التغييرات الآن',
                        showConfirmButton: false,
                        timer: 1500
                    });
                }
            });

            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();
        });
    </script>
}
