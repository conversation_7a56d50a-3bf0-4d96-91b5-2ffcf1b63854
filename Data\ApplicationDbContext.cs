﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
// ...existing code...
// No changes needed for claims logic in DbContext.
// ...existing code...
using Microsoft.EntityFrameworkCore;
using TajneedApp.Models;

namespace TajneedApp.Data;

public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public DbSet<Rank> Ranks { get; set; }
    public DbSet<Airbase> Airbase { get; set; }
    public DbSet<Category> Categories { get; set; }
    public DbSet<Exam> Exams { get; set; }
    public DbSet<Candidate> Candidates { get; set; }
    public DbSet<EvaluationPath> EvaluationPaths { get; set; }
    public DbSet<EvaluationComponent> EvaluationComponents { get; set; }
    public DbSet<CommitteeEvaluationCriteria> CommitteeEvaluationCriteria { get; set; }
    public DbSet<CandidateCommitteeEvaluation> CandidateCommitteeEvaluations { get; set; }
    public DbSet<CandidateComponentResult> CandidateComponentResults { get; set; }
    public DbSet<CandidateEvaluationProgress> CandidateEvaluationProgress { get; set; }
    public DbSet<CandidateExamResult> CandidateExamResults { get; set; }
  
  
    

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.Entity<ApplicationUser>()
            .HasIndex(u => u.ServiceNumber)
            .IsUnique();

        builder.Entity<ApplicationUser>()
            .HasOne(u => u.Rank)
            .WithMany(r => r.Users)
            .HasForeignKey(u => u.RankId);

        // EvaluationPath relationships
        builder.Entity<EvaluationPath>()
            .HasOne(p => p.Category)
            .WithMany(c => c.EvaluationPaths)
            .HasForeignKey(p => p.CategoryId)
            .OnDelete(DeleteBehavior.Cascade);

        // EvaluationComponent relationships
        builder.Entity<EvaluationComponent>()
            .HasOne(c => c.EvaluationPath)
            .WithMany(p => p.Components)
            .HasForeignKey(c => c.EvaluationPathId)
            .OnDelete(DeleteBehavior.Cascade);

        // CommitteeEvaluationCriteria relationships
        builder.Entity<CommitteeEvaluationCriteria>()
            .HasOne(c => c.Component)
            .WithMany(e => e.Criteria)
            .HasForeignKey(c => c.ComponentId)
            .OnDelete(DeleteBehavior.Cascade);

        // CandidateCommitteeEvaluation relationships
        builder.Entity<CandidateCommitteeEvaluation>()
            .HasOne(e => e.Candidate)
            .WithMany()
            .HasForeignKey(e => e.CandidateId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<CandidateCommitteeEvaluation>()
            .HasOne(e => e.Criteria)
            .WithMany(c => c.Evaluations)
            .HasForeignKey(e => e.CriteriaId)
            .OnDelete(DeleteBehavior.Cascade);

        // CandidateComponentResult relationships
        builder.Entity<CandidateComponentResult>()
            .HasOne(r => r.Candidate)
            .WithMany()
            .HasForeignKey(r => r.CandidateId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<CandidateComponentResult>()
            .HasOne(r => r.Component)
            .WithMany(c => c.ComponentResults)
            .HasForeignKey(r => r.ComponentId)
            .OnDelete(DeleteBehavior.Cascade);

        // CandidateEvaluationProgress relationships
        builder.Entity<CandidateEvaluationProgress>()
            .HasOne(p => p.Candidate)
            .WithMany()
            .HasForeignKey(p => p.CandidateId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<CandidateEvaluationProgress>()
            .HasOne(p => p.EvaluationPath)
            .WithMany(e => e.CandidateProgress)
            .HasForeignKey(p => p.EvaluationPathId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<CandidateEvaluationProgress>()
            .HasOne(p => p.CurrentComponent)
            .WithMany()
            .HasForeignKey(p => p.CurrentComponentId)
            .OnDelete(DeleteBehavior.SetNull);

        // CandidateExamResult relationships (keep for backward compatibility)
        builder.Entity<CandidateExamResult>()
            .HasOne(r => r.Candidate)
            .WithMany()
            .HasForeignKey(r => r.CandidateId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<CandidateExamResult>()
            .HasOne(r => r.Exam)
            .WithMany(e => e.ExamResults)
            .HasForeignKey(r => r.ExamId)
            .OnDelete(DeleteBehavior.NoAction);

        // Configure decimal precision for scores and percentages
        builder.Entity<CommitteeEvaluationCriteria>()
            .Property(c => c.MaxScore)
            .HasPrecision(5, 2);

        builder.Entity<CommitteeEvaluationCriteria>()
            .Property(c => c.WeightPercentage)
            .HasPrecision(5, 2);

        builder.Entity<CandidateCommitteeEvaluation>()
            .Property(e => e.Score)
            .HasPrecision(5, 2);

        builder.Entity<EvaluationComponent>()
            .Property(c => c.WeightPercentage)
            .HasPrecision(5, 2);

        builder.Entity<CandidateComponentResult>()
            .Property(r => r.Score)
            .HasPrecision(5, 2);

        builder.Entity<CandidateEvaluationProgress>()
            .Property(p => p.OverallScore)
            .HasPrecision(5, 2);

        builder.Entity<CandidateEvaluationProgress>()
            .Property(p => p.WeightedScore)
            .HasPrecision(5, 2);

        builder.Entity<CandidateEvaluationProgress>()
            .Property(p => p.CompletionPercentage)
            .HasPrecision(5, 2);

        builder.Entity<Exam>()
            .Property(e => e.WeightPercentage)
            .HasPrecision(5, 2);
    }
}
