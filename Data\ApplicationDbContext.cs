﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
// ...existing code...
// No changes needed for claims logic in DbContext.
// ...existing code...
using Microsoft.EntityFrameworkCore;
using TajneedApp.Models;

namespace TajneedApp.Data;

public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public DbSet<Rank> Ranks { get; set; }
    public DbSet<Airbase> Airbase { get; set; }
    public DbSet<Category> Categories { get; set; }
    public DbSet<Exam> Exams { get; set; }
    public DbSet<Candidate> Candidates { get; set; }
    public DbSet<CommitteeEvaluationCriteria> CommitteeEvaluationCriteria { get; set; }
    public DbSet<CandidateCommitteeEvaluation> CandidateCommitteeEvaluations { get; set; }
    public DbSet<CandidateExamResult> CandidateExamResults { get; set; }
  
  
    

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.Entity<ApplicationUser>()
            .HasIndex(u => u.ServiceNumber)
            .IsUnique();

        builder.Entity<ApplicationUser>()
            .HasOne(u => u.Rank)
            .WithMany(r => r.Users)
            .HasForeignKey(u => u.RankId);

        // CommitteeEvaluationCriteria relationships
        builder.Entity<CommitteeEvaluationCriteria>()
            .HasOne(c => c.Exam)
            .WithMany(e => e.CommitteeCriteria)
            .HasForeignKey(c => c.ExamId)
            .OnDelete(DeleteBehavior.Cascade);

        // CandidateCommitteeEvaluation relationships
        builder.Entity<CandidateCommitteeEvaluation>()
            .HasOne(e => e.Candidate)
            .WithMany()
            .HasForeignKey(e => e.CandidateId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<CandidateCommitteeEvaluation>()
            .HasOne(e => e.Criteria)
            .WithMany(c => c.Evaluations)
            .HasForeignKey(e => e.CriteriaId)
            .OnDelete(DeleteBehavior.Cascade);

        // CandidateExamResult relationships
        builder.Entity<CandidateExamResult>()
            .HasOne(r => r.Candidate)
            .WithMany()
            .HasForeignKey(r => r.CandidateId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<CandidateExamResult>()
            .HasOne(r => r.Exam)
            .WithMany(e => e.ExamResults)
            .HasForeignKey(r => r.ExamId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure decimal precision for scores and percentages
        builder.Entity<CommitteeEvaluationCriteria>()
            .Property(c => c.MaxScore)
            .HasPrecision(5, 2);

        builder.Entity<CommitteeEvaluationCriteria>()
            .Property(c => c.WeightPercentage)
            .HasPrecision(5, 2);

        builder.Entity<CandidateCommitteeEvaluation>()
            .Property(e => e.Score)
            .HasPrecision(5, 2);

        builder.Entity<Exam>()
            .Property(e => e.WeightPercentage)
            .HasPrecision(5, 2);
    }
}
