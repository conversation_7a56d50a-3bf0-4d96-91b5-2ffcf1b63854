using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using TajneedApp.Data;
using TajneedApp.Models;

namespace TajneedApp.Controllers
{
    public class AirbasesController : Controller
    {
        private readonly ApplicationDbContext _context;

        public AirbasesController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Airbases
        public async Task<IActionResult> Index()
        {
            return View(await _context.Airbase.ToListAsync());
        }

        // GET: Airbases/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var airbase = await _context.Airbase
                .FirstOrDefaultAsync(m => m.AirbaseId == id);
            if (airbase == null)
            {
                return NotFound();
            }

            return View(airbase);
        }

        // GET: Airbases/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Airbases/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("AirbaseId,AirbaseName")] Airbase airbase)
        {
            if (ModelState.IsValid)
            {
                _context.Add(airbase);
                await _context.SaveChangesAsync();
                return RedirectToAction(nameof(Index));
            }
            return View(airbase);
        }

        // GET: Airbases/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var airbase = await _context.Airbase.FindAsync(id);
            if (airbase == null)
            {
                return NotFound();
            }
            return View(airbase);
        }

        // POST: Airbases/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("AirbaseId,AirbaseName")] Airbase airbase)
        {
            if (id != airbase.AirbaseId)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(airbase);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!AirbaseExists(airbase.AirbaseId))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(airbase);
        }

        // GET: Airbases/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var airbase = await _context.Airbase
                .FirstOrDefaultAsync(m => m.AirbaseId == id);
            if (airbase == null)
            {
                return NotFound();
            }

            return View(airbase);
        }

        // POST: Airbases/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var airbase = await _context.Airbase.FindAsync(id);
            if (airbase != null)
            {
                _context.Airbase.Remove(airbase);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool AirbaseExists(int id)
        {
            return _context.Airbase.Any(e => e.AirbaseId == id);
        }
    }
}
