@model TajneedApp.Models.Exam

@{
    ViewData["Title"] = "تعديل امتحان";
}

<h1>تعديل امتحان</h1>

<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="ExamId" />
            <div class="form-group">
                <label asp-for="ExamName" class="control-label"></label>
                <input asp-for="ExamName" class="form-control" />
                <span asp-validation-for="ExamName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CategoryId" class="control-label"></label>
                <select asp-for="CategoryId" class="form-control" asp-items="ViewBag.CategoryId"></select>
                <span asp-validation-for="CategoryId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="EvaluationType" class="control-label"></label>
                <select asp-for="EvaluationType" class="form-control"></select>
                <span asp-validation-for="EvaluationType" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="PassingScore" class="control-label"></label>
                <input asp-for="PassingScore" class="form-control" />
                <span asp-validation-for="PassingScore" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="MaxScore" class="control-label"></label>
                <input asp-for="MaxScore" class="form-control" />
                <span asp-validation-for="MaxScore" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
