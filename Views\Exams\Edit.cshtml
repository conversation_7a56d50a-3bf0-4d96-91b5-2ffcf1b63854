@model TajneedApp.Models.Exam

@{
    ViewData["Title"] = "تعديل امتحان";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>تعديل امتحان: @Model.ExamName
                    </h4>
                </div>
                <div class="card-body">
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form asp-action="Edit" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        <input type="hidden" asp-for="ExamId" />

                        <div class="row">
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label asp-for="ExamName" class="form-label fw-bold">
                                        <i class="fas fa-file-alt me-1"></i>@Html.DisplayNameFor(m => m.ExamName)
                                    </label>
                                    <input asp-for="ExamName" class="form-control" placeholder="أدخل اسم الامتحان" />
                                    <span asp-validation-for="ExamName" class="text-danger small"></span>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label asp-for="CategoryId" class="form-label fw-bold">
                                        <i class="fas fa-tags me-1"></i>@Html.DisplayNameFor(m => m.CategoryId)
                                    </label>
                                    <select asp-for="CategoryId" class="form-select" asp-items="ViewBag.CategoryId">
                                        <option value="">اختر الفئة</option>
                                    </select>
                                    <span asp-validation-for="CategoryId" class="text-danger small"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label asp-for="EvaluationType" class="form-label fw-bold">
                                        <i class="fas fa-chart-line me-1"></i>@Html.DisplayNameFor(m => m.EvaluationType)
                                    </label>
                                    <select asp-for="EvaluationType" class="form-select" asp-items="ViewBag.EvaluationType" id="evaluationType">
                                        <option value="">اختر نوع التقييم</option>
                                    </select>
                                    <span asp-validation-for="EvaluationType" class="text-danger small"></span>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label asp-for="MaxScore" class="form-label fw-bold">
                                        <i class="fas fa-star me-1"></i>@Html.DisplayNameFor(m => m.MaxScore)
                                    </label>
                                    <input asp-for="MaxScore" class="form-control" type="number" min="1" placeholder="أدخل الدرجة القصوى" />
                                    <span asp-validation-for="MaxScore" class="text-danger small"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label asp-for="PassingScore" class="form-label fw-bold">
                                        <i class="fas fa-check-circle me-1"></i>@Html.DisplayNameFor(m => m.PassingScore)
                                    </label>
                                    <input asp-for="PassingScore" class="form-control" type="number" min="1" placeholder="أدخل الدرجة المطلوبة" />
                                    <span asp-validation-for="PassingScore" class="text-danger small"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save me-2"></i>حفظ التعديلات
                                    </button>
                                    <a asp-action="Details" asp-route-id="@Model.ExamId" class="btn btn-info">
                                        <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                    </a>
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Show/hide score fields based on evaluation type
            $('#evaluationType').change(function() {
                var selectedValue = $(this).val();
                var passingScoreDiv = $('input[name="PassingScore"]').closest('.mb-3');
                var maxScoreDiv = $('input[name="MaxScore"]').closest('.mb-3');

                if (selectedValue === '1') { // Mark (درجة)
                    passingScoreDiv.show();
                    maxScoreDiv.show();
                    $('input[name="PassingScore"]').attr('required', true);
                    $('input[name="MaxScore"]').attr('required', true);
                } else if (selectedValue === '2' || selectedValue === '3') { // PassFail or FitInfit
                    passingScoreDiv.hide();
                    maxScoreDiv.hide();
                    $('input[name="PassingScore"]').attr('required', false).val('');
                    $('input[name="MaxScore"]').attr('required', false).val('');
                }
            });

            // Trigger change event on page load
            $('#evaluationType').trigger('change');

            // Validate passing score is less than max score
            $('input[name="PassingScore"], input[name="MaxScore"]').on('input', function() {
                var passingScore = parseInt($('input[name="PassingScore"]').val()) || 0;
                var maxScore = parseInt($('input[name="MaxScore"]').val()) || 0;

                if (passingScore > 0 && maxScore > 0 && passingScore >= maxScore) {
                    $('input[name="PassingScore"]').addClass('is-invalid');
                    if (!$('input[name="PassingScore"]').next('.invalid-feedback').length) {
                        $('input[name="PassingScore"]').after('<div class="invalid-feedback">الدرجة المطلوبة يجب أن تكون أقل من الدرجة القصوى</div>');
                    }
                } else {
                    $('input[name="PassingScore"]').removeClass('is-invalid');
                    $('input[name="PassingScore"]').next('.invalid-feedback').remove();
                }
            });
        });
    </script>
}
