using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace TajneedApp.Models;

public enum ResultStatus
{
    [Display(Name = "قيد التقييم")]
    InProgress = 1,
    [Display(Name = "مكتمل")]
    Completed = 2,
    [Display(Name = "ناجح")]
    Passed = 3,
    [Display(Name = "راسب")]
    Failed = 4,
    [Display(Name = "لائق")]
    Fit = 5,
    [Display(Name = "غير لائق")]
    Unfit = 6
}

public partial class CandidateComponentResult
{
    [Key]
    public int ResultId { get; set; }

    [Display(Name = "معرف المرشح")]
    [Required(ErrorMessage = "حقل معرف المرشح مطلوب.")]
    public int CandidateId { get; set; }

    [Display(Name = "معرف مكون التقييم")]
    [Required(ErrorMessage = "حقل معرف مكون التقييم مطلوب.")]
    public int ComponentId { get; set; }

    [Display(Name = "الدرجة")]
    [Range(0, 100, ErrorMessage = "الدرجة يجب أن تكون بين 0 و 100")]
    public decimal? Score { get; set; }

    [Display(Name = "حالة النتيجة")]
    [Required(ErrorMessage = "حقل حالة النتيجة مطلوب.")]
    public ResultStatus Status { get; set; }

    [Display(Name = "النتيجة النصية")]
    [StringLength(100, ErrorMessage = "النتيجة النصية يجب أن لا تتجاوز 100 حرف.")]
    public string? TextResult { get; set; }

    [Display(Name = "تاريخ التقييم")]
    [Required(ErrorMessage = "حقل تاريخ التقييم مطلوب.")]
    [DataType(DataType.DateTime)]
    public DateTime EvaluationDate { get; set; } = DateTime.UtcNow;

    [Display(Name = "المقيم")]
    [Required(ErrorMessage = "حقل المقيم مطلوب.")]
    public string? EvaluatedBy { get; set; }

    [Display(Name = "ملاحظات")]
    [StringLength(1000, ErrorMessage = "الملاحظات يجب أن لا تتجاوز 1000 حرف.")]
    public string? Comments { get; set; }

    [Display(Name = "تاريخ الإنشاء")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    [Display(Name = "المستخدم المنشئ")]
    public string? CreatedBy { get; set; }

    [Display(Name = "تاريخ آخر تعديل")]
    public DateTime? LastModifiedDate { get; set; }

    [Display(Name = "المستخدم المعدل")]
    public string? LastModifiedBy { get; set; }

    // Navigation Properties
    [ValidateNever]
    [Display(Name = "المرشح")]
    public virtual Candidate? Candidate { get; set; }

    [ValidateNever]
    [Display(Name = "مكون التقييم")]
    public virtual EvaluationComponent? Component { get; set; }
}
