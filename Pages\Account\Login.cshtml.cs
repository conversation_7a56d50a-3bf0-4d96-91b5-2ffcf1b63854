using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using TajneedApp.Data;
using TajneedApp.Models;

namespace TajneedApp.Pages.Account
{
    public class LoginModel : PageModel
    {
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;

        public LoginModel(
            SignInManager<ApplicationUser> signInManager,
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context)
        {
            _signInManager = signInManager;
            _userManager = userManager;
            _context = context;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public string? ReturnUrl { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public class InputModel
        {
            [Required(ErrorMessage = "الرقم العسكري مطلوب")]
            [Display(Name = "الرقم العسكري")]
            public string ServiceNumber { get; set; } = string.Empty;

            [Required(ErrorMessage = "كلمة المرور مطلوبة")]
            [DataType(DataType.Password)]
            [Display(Name = "كلمة المرور")]
            public string Password { get; set; } = string.Empty;

            [Display(Name = "تذكرني")]
            public bool RememberMe { get; set; }
        }

        public async Task OnGetAsync(string? returnUrl = null)
        {
            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                ModelState.AddModelError(string.Empty, ErrorMessage);
            }

            returnUrl ??= Url.Content("~/");

            // Clear the existing external cookie to ensure a clean login process
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);

            ReturnUrl = returnUrl;
        }

        public async Task<IActionResult> OnPostAsync(string? returnUrl = null)
        {
            returnUrl ??= Url.Content("~/");

            if (ModelState.IsValid)
            {
                var user = await _userManager.Users
                    .Include(u => u.Rank)
                    .FirstOrDefaultAsync(u => u.ServiceNumber == Input.ServiceNumber);

                if (user != null)
                {
                    var result = await _signInManager.PasswordSignInAsync(
                        user.UserName!,
                        Input.Password,
                        Input.RememberMe,
                        lockoutOnFailure: false);

                    if (result.Succeeded)
                    {
                        // Add custom claims if not already present
                        var claims = await _userManager.GetClaimsAsync(user);
                        var claimsToAdd = new List<Claim>();

                        if (!claims.Any(c => c.Type == "FullName"))
                            claimsToAdd.Add(new Claim("FullName", user.FullName ?? ""));

                        if (!claims.Any(c => c.Type == "Rank"))
                        {
                            var rankName = user.Rank?.RankName ?? "";
                            claimsToAdd.Add(new Claim("Rank", rankName));
                        }

                        if (!claims.Any(c => c.Type == "ServiceNumber"))
                            claimsToAdd.Add(new Claim("ServiceNumber", user.ServiceNumber ?? ""));

                        if (!claims.Any(c => c.Type == "IsActive"))
                            claimsToAdd.Add(new Claim("IsActive", user.IsActive.ToString()));

                        if (claimsToAdd.Count > 0)
                            await _userManager.AddClaimsAsync(user, claimsToAdd);

                        // Re-sign in to update claims in cookie
                        await _signInManager.SignInAsync(user, isPersistent: Input.RememberMe);

                        return LocalRedirect(returnUrl);
                    }

                    if (result.IsLockedOut)
                    {
                        return RedirectToPage("./Lockout");
                    }
                    else
                    {
                        ModelState.AddModelError(string.Empty, "محاولة دخول غير صحيحة. يرجى التحقق من الرقم العسكري وكلمة المرور.");
                        return Page();
                    }
                }
                else
                {
                    ModelState.AddModelError(string.Empty, "الرقم العسكري غير موجود.");
                    return Page();
                }
            }

            // If we got this far, something failed, redisplay form
            return Page();
        }
    }
}
