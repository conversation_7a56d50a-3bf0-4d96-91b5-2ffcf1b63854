// ...existing usings...
using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using TajneedApp.Models;
// ...existing code...

public class LoginModel : PageModel
{
    // ...existing fields...

    private readonly SignInManager<ApplicationUser> _signInManager;
    private readonly UserManager<ApplicationUser> _userManager;
    // ...existing code...

    public LoginModel(SignInManager<ApplicationUser> signInManager, UserManager<ApplicationUser> userManager /*, ...*/)
    {
        _signInManager = signInManager;
        _userManager = userManager;
        // ...existing code...
    }

    // ...existing code...

    public async Task<IActionResult> OnPostAsync(string? returnUrl = null)
    {
        // ...existing code for model validation...

        var user = await _userManager.FindByNameAsync(Input.ServiceNumber);
        if (user != null)
        {
            // ...existing password check...
            var result = await _signInManager.PasswordSignInAsync(user, Input.Password, Input.RememberMe, lockoutOnFailure: false);
            if (result.Succeeded)
            {
                // Add custom claims if not already present
                var claims = await _userManager.GetClaimsAsync(user);
                var claimsToAdd = new List<Claim>();

                if (!claims.Any(c => c.Type == "FullName"))
                    claimsToAdd.Add(new Claim("FullName", user.FullName ?? ""));
                if (!claims.Any(c => c.Type == "Rank"))
                {
                    // Fetch rank name if needed
                    var rankName = user.Rank?.RankName;
                    if (string.IsNullOrEmpty(rankName) && user.RankId > 0)
                    {
                        // If Rank navigation property is not loaded, fetch from DB
                        // Assuming _context is injected or available
                        rankName = _context.Ranks.FirstOrDefault(r => r.RankId == user.RankId)?.RankName ?? "";
                    }
                    claimsToAdd.Add(new Claim("Rank", rankName ?? ""));
                }
                if (!claims.Any(c => c.Type == "ServiceNumber"))
                    claimsToAdd.Add(new Claim("ServiceNumber", user.ServiceNumber ?? ""));
                if (!claims.Any(c => c.Type == "IsActive"))
                    claimsToAdd.Add(new Claim("IsActive", user.IsActive.ToString()));

                if (claimsToAdd.Count > 0)
                    await _userManager.AddClaimsAsync(user, claimsToAdd);

                // Re-sign in to update claims in cookie
                await _signInManager.SignInAsync(user, isPersistent: Input.RememberMe);

                // ...existing redirect logic...
            }
            // ...existing code...
        }
        // ...existing code...
    }
    // ...existing code...
}
