using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace TajneedApp.Models;

public enum EvaluationStatus
{
    [Display(Name = "لم يبدأ")]
    NotStarted = 1,
    [Display(Name = "قيد التقييم")]
    InProgress = 2,
    [Display(Name = "مكتمل")]
    Completed = 3,
    [Display(Name = "ناجح")]
    Passed = 4,
    [Display(Name = "راسب")]
    Failed = 5,
    [Display(Name = "معلق")]
    OnHold = 6
}

public partial class CandidateEvaluationProgress
{
    [Key]
    public int ProgressId { get; set; }

    [Display(Name = "معرف المرشح")]
    [Required(ErrorMessage = "حقل معرف المرشح مطلوب.")]
    public int CandidateId { get; set; }

    [Display(Name = "معرف مسار التقييم")]
    [Required(ErrorMessage = "حقل معرف مسار التقييم مطلوب.")]
    public int EvaluationPathId { get; set; }

    [Display(Name = "حالة التقييم")]
    [Required(ErrorMessage = "حقل حالة التقييم مطلوب.")]
    public EvaluationStatus Status { get; set; } = EvaluationStatus.NotStarted;

    [Display(Name = "تاريخ البدء")]
    [DataType(DataType.DateTime)]
    public DateTime? StartDate { get; set; }

    [Display(Name = "تاريخ الانتهاء")]
    [DataType(DataType.DateTime)]
    public DateTime? CompletionDate { get; set; }

    [Display(Name = "النتيجة الإجمالية")]
    [Range(0, 100, ErrorMessage = "النتيجة الإجمالية يجب أن تكون بين 0 و 100")]
    public decimal? OverallScore { get; set; }

    [Display(Name = "النتيجة المرجحة")]
    [Range(0, 100, ErrorMessage = "النتيجة المرجحة يجب أن تكون بين 0 و 100")]
    public decimal? WeightedScore { get; set; }

    [Display(Name = "نسبة الإنجاز (%)")]
    [Range(0, 100, ErrorMessage = "نسبة الإنجاز يجب أن تكون بين 0 و 100")]
    public decimal CompletionPercentage { get; set; } = 0;

    [Display(Name = "المكون الحالي")]
    public int? CurrentComponentId { get; set; }

    [Display(Name = "ملاحظات")]
    [StringLength(1000, ErrorMessage = "الملاحظات يجب أن لا تتجاوز 1000 حرف.")]
    public string? Notes { get; set; }

    [Display(Name = "تاريخ الإنشاء")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    [Display(Name = "المستخدم المنشئ")]
    public string? CreatedBy { get; set; }

    [Display(Name = "تاريخ آخر تعديل")]
    public DateTime? LastModifiedDate { get; set; }

    [Display(Name = "المستخدم المعدل")]
    public string? LastModifiedBy { get; set; }

    // Navigation Properties
    [ValidateNever]
    [Display(Name = "المرشح")]
    public virtual Candidate? Candidate { get; set; }

    [ValidateNever]
    [Display(Name = "مسار التقييم")]
    public virtual EvaluationPath? EvaluationPath { get; set; }

    [ValidateNever]
    [Display(Name = "المكون الحالي")]
    public virtual EvaluationComponent? CurrentComponent { get; set; }
}
