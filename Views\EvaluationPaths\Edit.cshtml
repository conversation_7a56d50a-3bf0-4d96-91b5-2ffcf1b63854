@model TajneedApp.Models.EvaluationPath

@{
    ViewData["Title"] = "تعديل مسار التقييم";
}

<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h3 class="m-0 font-weight-bold">تعديل مسار التقييم</h3>
        </div>
        <div class="card-body">
            <form asp-action="Edit">
                <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                
                <input type="hidden" asp-for="EvaluationPathId" />
                <input type="hidden" asp-for="CreatedDate" />
                <input type="hidden" asp-for="CreatedBy" />
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="PathName" class="form-label"></label>
                            <input asp-for="PathName" class="form-control" />
                            <span asp-validation-for="PathName" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label asp-for="CategoryId" class="form-label"></label>
                            <select asp-for="CategoryId" class="form-control" asp-items="ViewBag.CategoryId">
                                <option value="">اختر الفئة</option>
                            </select>
                            <span asp-validation-for="CategoryId" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-3">
                    <label asp-for="Description" class="form-label"></label>
                    <textarea asp-for="Description" class="form-control" rows="4"></textarea>
                    <span asp-validation-for="Description" class="text-danger"></span>
                </div>

                <div class="form-group mb-3">
                    <div class="form-check">
                        <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                        <label asp-for="IsActive" class="form-check-label">
                            تفعيل المسار
                        </label>
                    </div>
                    <small class="form-text text-muted">إلغاء التفعيل سيمنع استخدام هذا المسار في تقييمات جديدة</small>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> تعديل المسار قد يؤثر على التقييمات الجارية. تأكد من مراجعة التغييرات قبل الحفظ.
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                    <a asp-action="Details" asp-route-id="@Model.EvaluationPathId" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> العودة للتفاصيل
                    </a>
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="fas fa-list"></i> العودة للقائمة
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
