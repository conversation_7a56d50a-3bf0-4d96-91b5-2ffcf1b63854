using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace TajneedApp.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [StringLength(20)]
        public string? ServiceNumber { get; set; }
        [Required]

        public string? FullName { get; set; }
        
        public int RankId { get; set; }

        public string? Position { get; set; }

        // خصائص التقييم
        public bool IsCommitteeMember { get; set; }
        public bool IsCommitteeChair { get; set; }
        public bool IsActive { get; set; }
        [ValidateNever]
        public virtual Rank? Rank { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
}
