<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام إدارة التجنيد</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
 
  
    <!-- Datatable CSS -->
    
    <link href="~/datatable/css/datatable-bootstrap.css" rel="stylesheet" />
    <link href="~/datatable/css/datatable.css" rel="stylesheet" />
    <link href="https://cdn.datatables.net/2.2.2/css/dataTables.dataTables.min.css" rel="stylesheet" />


    <!-- Datatable library -->
    <link href="~/datatable/css/datatable.css" rel="stylesheet" />
    <link href="~/datatable/css/datatable.min.css" rel="stylesheet" />



    <!-- Syncfusion ASP.NET Core controls styles -->
    <link rel="stylesheet" href="https://cdn.syncfusion.com/ej2/29.1.33/fluent.css" />
    <!-- Syncfusion ASP.NET Core controls scripts -->
    <script src="https://cdn.syncfusion.com/ej2/29.1.33/dist/ej2.min.js"></script>
    <link rel="stylesheet" href="~/css/sidebar.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
</head>
<body>
    <div class="sidebar">
        <div class="brand-container">
            <h5 class=" menu-text text-primary  mb-0 fw-bolder" style="font-size: 20px" > نظام إدارة التجنيد </h5>
            <button class="toggle-btn">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        @if (User.Identity.IsAuthenticated)
        {
            <ul class="nav flex-column">

                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="collapse" href="#controlPanel">
                        <i class="fas fa-cogs"></i>
                        <span class="menu-text">لوحة التحكم</span>
                        <i class="fas fa-chevron-down mr-auto"></i>
                    </a>

                    <div class="collapse submenu" id="controlPanel">
                        <a class="nav-link" asp-controller="Categories" asp-action="Index">
                            <i class="fas fa-users"></i>
                            <span class="menu-text">فئات المرشحين</span>
                        </a> <a class="nav-link" asp-controller="Home" asp-action="Dashboard">
                            <i class="fas fa-users"></i>
                            <span class="menu-text">لوحة المعلومات </span>
                        </a>

                        <a class="nav-link" asp-controller="Ranks" asp-action="Index">
                            <i class="fas fa-circle-user"></i>
                            <span class="menu-text"> الرتبة العسكرية</span>
                        </a>

                        <a class="nav-link" asp-controller="Airbases" asp-action="Index">
                            <i class="fas fa-users"></i>
                            <span class="menu-text">القواعد الجوية</span>
                        </a>
                        <a class="nav-link" asp-controller="Candidates" asp-action="Index">
                            <i class="fas fa-users"></i>
                            <span class="menu-text">أسماء المرشحين</span>
                        </a>
                    
                        <a class="nav-link" href="#">
                            <i class="fas fa-user-tie"></i>
                            <span class="menu-text">أعضاء اللجنة</span>
                        </a>
                        <a class="nav-link" href="#">
                            <i class="fas fa-key"></i>
                            <span class="menu-text">الصلاحيات</span>
                        </a>
                        <a class="nav-link" asp-controller="User" asp-action="Index">
                            <i class="fas fa-users-cog"></i>
                            <span class="menu-text">إدارة المستخدمين</span>
                        </a>
                    </div>
                </li>
            </ul>
        }

    </div>

    <div class="main-content">
        <main role="main" class="pb-1">
           @* This is to show user information and display it at top of the page*@
                <partial name="_UserInfoPartial" />
          
            @RenderBody()
        </main>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/datatable/js/datatable.jquery.js"></script>
    <script src="~/datatable/js/datatable.jquery.min.js"></script>
    <script src="~/datatable/js/datatable.js"></script>
    <script src="~/datatable/js/datatable.min.js"></script>

    <script src="~/js/site.js" asp-append-version="true"></script>

<!-- jQuery (required for DataTables) -->
<script src="~/lib/jquery/dist/jquery.min.js"></script>

<!-- Bootstrap JS (optional, if using Bootstrap-styled DataTables) -->
<script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

<!-- DataTables JS -->
<script src="~/datatable/js/datatable.jquery.min.js"></script>
<script src="~/datatable/js/datatable.min.js"></script>
<script src="https://cdn.datatables.net/2.2.2/js/dataTables.min.js"></script>
    <!-- Syncfusion ASP.NET Core controls scripts -->

<!-- Custom JS -->
<script src="~/js/site.js" asp-append-version="true"></script>

 
    <script>
        document.querySelector('.toggle-btn').addEventListener('click', function () {
            document.querySelector('.sidebar').classList.toggle('collapsed');
           
            document.querySelector('.main-content').classList.toggle('expanded');
        });


    </script>

    @await RenderSectionAsync("Scripts", required: false)
    <ejs-scripts></ejs-scripts>
   
</body>
</html>