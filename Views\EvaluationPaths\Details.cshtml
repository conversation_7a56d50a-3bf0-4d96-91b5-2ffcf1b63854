@model TajneedApp.Models.EvaluationPath

@{
    ViewData["Title"] = "تفاصيل مسار التقييم";
}

<div class="container-fluid">
    <!-- Path Information Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h3 class="m-0 font-weight-bold">@Model.PathName</h3>
            <div>
                @if (Model.IsActive)
                {
                    <span class="badge bg-success fs-6">نشط</span>
                }
                else
                {
                    <span class="badge bg-warning fs-6">غير نشط</span>
                }
            </div>
        </div>
        <div class="card-body">
            @if (TempData["Success"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>@TempData["Success"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            <div class="row">
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-sm-4">الفئة:</dt>
                        <dd class="col-sm-8">
                            <span class="badge bg-info">@Model.Category?.CategoryName</span>
                        </dd>
                        <dt class="col-sm-4">تاريخ الإنشاء:</dt>
                        <dd class="col-sm-8">@Model.CreatedDate.ToString("yyyy-MM-dd HH:mm")</dd>
                        @if (Model.LastModifiedDate.HasValue)
                        {
                            <dt class="col-sm-4">آخر تعديل:</dt>
                            <dd class="col-sm-8">@Model.LastModifiedDate.Value.ToString("yyyy-MM-dd HH:mm")</dd>
                        }
                    </dl>
                </div>
                <div class="col-md-6">
                    <dl class="row">
                        <dt class="col-sm-4">عدد المكونات:</dt>
                        <dd class="col-sm-8">
                            <span class="badge bg-secondary">@Model.Components.Count مكون</span>
                        </dd>
                        <dt class="col-sm-4">الوصف:</dt>
                        <dd class="col-sm-8">@Model.Description</dd>
                    </dl>
                </div>
            </div>

            <div class="mt-3">
                <a asp-action="Edit" asp-route-id="@Model.EvaluationPathId" class="btn btn-primary">
                    <i class="fas fa-edit"></i> تعديل المسار
                </a>
                <a asp-controller="EvaluationComponents" asp-action="Create" asp-route-pathId="@Model.EvaluationPathId" class="btn btn-success">
                    <i class="fas fa-plus"></i> إضافة مكون جديد
                </a>
                <a asp-action="Clone" asp-route-id="@Model.EvaluationPathId" class="btn btn-secondary">
                    <i class="fas fa-copy"></i> نسخ المسار
                </a>
                <a asp-action="Index" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>

    <!-- Components Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h5 class="m-0 font-weight-bold">مكونات التقييم</h5>
            <a asp-controller="EvaluationComponents" asp-action="Create" asp-route-pathId="@Model.EvaluationPathId" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> إضافة مكون
            </a>
        </div>
        <div class="card-body">
            @if (Model.Components.Any())
            {
                <div class="table-responsive">
                    <table class="table table-bordered table-hover table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>الترتيب</th>
                                <th>اسم المكون</th>
                                <th>النوع</th>
                                <th>نوع التقييم</th>
                                <th>الوزن %</th>
                                <th>المعايير</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var component in Model.Components.OrderBy(c => c.DisplayOrder))
                            {
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">@component.DisplayOrder</span>
                                    </td>
                                    <td>
                                        <strong>@component.ComponentName</strong>
                                        @if (!string.IsNullOrEmpty(component.ComponentDescription))
                                        {
                                            <br><small class="text-muted">@component.ComponentDescription</small>
                                        }
                                    </td>
                                    <td>
                                        <span class="badge bg-info">@component.ComponentType.GetDisplayName()</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">@component.EvaluationType.GetDisplayName()</span>
                                    </td>
                                    <td>
                                        <strong>@component.WeightPercentage%</strong>
                                    </td>
                                    <td>
                                        @if (component.ComponentType == ComponentType.CommitteeEvaluation)
                                        {
                                            <span class="badge bg-warning">@component.Criteria.Count معيار</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        @if (component.IsActive)
                                        {
                                            <span class="badge bg-success">نشط</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-warning">غير نشط</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a class="btn btn-sm btn-info" asp-controller="EvaluationComponents" asp-action="Details" asp-route-id="@component.ComponentId" title="التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a class="btn btn-sm btn-primary" asp-controller="EvaluationComponents" asp-action="Edit" asp-route-id="@component.ComponentId" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if (component.ComponentType == ComponentType.CommitteeEvaluation)
                                            {
                                                <a class="btn btn-sm btn-success" asp-controller="CommitteeEvaluationCriteria" asp-action="Index" asp-route-componentId="@component.ComponentId" title="إدارة المعايير">
                                                    <i class="fas fa-list"></i>
                                                </a>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- Weight Summary -->
                <div class="mt-3">
                    @{
                        var totalWeight = Model.Components.Where(c => c.IsActive).Sum(c => c.WeightPercentage);
                    }
                    <div class="alert @(totalWeight == 100 ? "alert-success" : "alert-warning")">
                        <i class="fas fa-calculator me-2"></i>
                        <strong>إجمالي الأوزان:</strong> @totalWeight%
                        @if (totalWeight != 100)
                        {
                            <span class="ms-2">(يجب أن يكون المجموع 100%)</span>
                        }
                    </div>
                </div>
            }
            else
            {
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مكونات تقييم</h5>
                    <p class="text-muted">ابدأ بإضافة مكونات التقييم لهذا المسار</p>
                    <a asp-controller="EvaluationComponents" asp-action="Create" asp-route-pathId="@Model.EvaluationPathId" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة أول مكون
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Add any JavaScript for interactive features
    </script>
}
