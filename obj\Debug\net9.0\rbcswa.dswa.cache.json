{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["4bLQJaiwON0JerB0MwbCfyaIVGyNWnrIJ+oQ40EjLmA=", "9ZTp7yMDzhBos0Mryf4Jag4ROIw5etyq6jheLyjBTV4=", "0u0M3UJGwlKn3nnWZoHBf6oPUgvxmVmqvMpmRBxfKYI=", "heJH2a/YKpjXepmKsJq9m7S5sfjqFmyqSEaOaIrSo4o=", "J95Hz6EwCT53xc5rDFs7R0OevgprkT7px3BjBfWU5s4=", "Z76PYICrt6T7dccGSEo3mWHYWD8dZRJ5OQL0jI2SWqY=", "AukLs3gRQdmxvHCxeir32tO1mlWZOWOaup7dzV/bYPc=", "r1KMRyNh24HHoma+sa0ONw6l91P36quUELrjbnMa+gw=", "ln5KiVbcUlsR9hm3x517flNGKYPi4GehLEcdSoUpmtc=", "Frzii7wF1NnX3lq4qIRDiLc+QcD6n5rQCUfwZ96fr5M=", "iMOdgBYVz7uSS4G9T6rOjsFluy2Qf11XvxKUZTGRVo0=", "XVYj7p7MHjI7nz6Ki54qX/uU6uJ4UQTwZE75w+x3XX0=", "C+ZC4GDT+RARfZrLrf6czwG9ZbNx+JzdFYzxw2dthLM=", "ZC9AVXZ8xxkf7a252PZrFuQyCiNL59KdQgVTnep8PxY=", "BfDyfSh0j+iqXDiXOwOszVXhpGsDtoWQQ782Zn9ypnU=", "d18UtfrN4r4YK8ceTkq75l3m/zyxWZdxI4hEeoX7FPg=", "a3hfmyQE/tHFpxRGb3gj46H6hGT38bRxLBfswAPM+YY=", "ltR7BG0mcWjnBLIVDF6yAdnT4k+k30O8VutpzR4sihg=", "7h/AJtYCdqPxjegpctb2XBdbyNg00ly528X2jZPrNoU=", "JUkhGSPwUXkPof7L9R7QZbCF/JlDu6DtunH7l7uY8Hw=", "j13r9Lj8PQuvl2O+ueEX6wSsacKA+WGGwmVyhWy919E=", "Frw5yWra9OSq1YTkFgXL5+LiW7cPTHtdLNfuiK8t84Y=", "q5CfsVlZv3xegktIn73eRNsX1AYI2dL/hO6qlFjtcbM=", "Muwijw5oZuNcJzAZ7ytkjkE3lRsLGN+4epLMJZk4Sq8=", "NJrxBz01MEV4ZOOB1rBsECjR8E6IOj1e2AynK+hfJZg=", "UiJ4jXJf8jNN61wQlgTqfhwvwwm8Xcg6MeOZ4s/deqc=", "nAzjFFXkS6gJXPyjDNU+dSvxbRidId/nzi2kr4znJRY=", "UJ57z4qUd8lOv0lHTEuPr508177KfiQA0Ze70bLlup0=", "+2Zmb7YIcl+OJiLRYrTzhCvL374nUWBxgizCy/VftlM=", "mHHuDFEc4yyEXhe/WWH5Ydag4kqYIfRio00LLGFTYbs=", "3fPTp8cqYR5Cdk1NEmAROyHxTuWQx8VjOw8peA/xMk8=", "AY3+hsQbOsIdZ3REsrYSm3fv0Lr4aYS80Wizi3qgAG8=", "HVisagfB07jwh9n1jsX9Ne0X/954mI+qH02AKS5NjyQ=", "8lQho+4TRFfF9jg52aK4O2jnsHmSb/acGyTX38idH2E=", "HqyVhFy1k8mRBd+OpDUaL31M2sQQR7P5hzLgZxjNVmw=", "67rB9OqC6bCpuJrgQvhQe+RIjrojsiP2a42HFATBEAM=", "9HhaC0C3gx5/R87EvRa7WHpEKUX4MaBPIwCgpYfDHDw=", "nUxgCk/h6sZ38lenqSwcYIAFsgax6LSEXg8BvTum4wk=", "3T9c8yxSA0UvlvC1gvBoTZyd9tZtGx+gv+bbTUoBBG8=", "/hBflUHTvLlh3GA6SWEce9zR2PJgEEt2e7f2o20R42U=", "snpU+40thaAzMSZXjH0dyn2iGzqSzIRP1SIkxTI/Xj8=", "TMo6vJW9s/325K7F75cqiAliFOY9vhWzOTq80qPrRmc=", "XnMb/ZJQ/z2fc5CB3bPKZ4i4pjorx045qrJhyN5z6lk=", "0WfMTM9WhkSCxEQr7ys1GdGfuanO0NwoKrjpU/jmoOk=", "mYv0c9NrrHdZb+oXGfuUv7mTcU6KMpA3VZiD6yQMbYU=", "EHtX+CEGvtO7wZTu0HU43JNI22Swf/F0NVP2O3hAF0k=", "pZ6OJd/RgmtzDr+dZEB9Pux0SiTwDeKZb4CArtvsTz0=", "SvL49To5hamDjLBQMz+y5C0QDA0CV6BevDqYIq8MGWU=", "QpfGJaym8viiliGHov1ZcJeUDTRmk6OxXuHL3VwwH5c=", "gu11ADHUClY1PDkKPVsN6GCErrzuFbzsGFsUEvqvWTk=", "SwnaE9OTiI8A23I//OtQcE7yqK6djbbNTdC6jdoZqe0=", "p7fnFz8z4cXGgMkgwcqA4dmBJqozjd6WnUInOvH4i1o=", "j2a6wH3wJFhFPK5jq42R+2c6+3IIEHTVmmimNFTjMmk=", "edoHiozjQMF7Burp3K9lgLmHw6vzPmSJmVBfYjZEpMU=", "N/vbAf6HEm+TsxPxIsB8yCaaB6i03Z8032a2d8fVb44=", "wN+ej3PTx+ZUfNJuaxPzhmOTlbJHc6KWQ7UTNT8RKug=", "5A895m1V7FgRQbZ32c2Xjgcp/Qg0OMR09OzsskqRVWs=", "Bv6RRdLDcnXL/e66sguXNn6tzs3kcrMDjBB1s/quuDU=", "c7vOQQHtlzM/TOrZCof4Z6t2bjVCnJlkS06MSqVTCSk=", "+YDaKeHs78scTInUc/QK6sde8bl2YaGJSocud3dPFrw=", "2KLfvCXlq3A59AUo02LY5MBlKhnd2wvfSE8epiBbzyo=", "AAGXh47nSk4X5sN7SreTC10YkSsqaJRYWFN7rDLig50=", "Os77d/sdhKJaVvgd1tz2yrJKB/DPZm2x+zZNDa8mCqA=", "4EOF3DQXr9WlqvRXOB12ERlUlsc66354HyzGVInCEZk=", "6NlJHO9/TB3YreywaJAEqdyfN5X0dE13M2aMgj2saXU=", "6IK+/8eIUn4rpMP5Zh06VksNjX4u7oMBDBYWYq1XNnM=", "Pttxh6STR7O8Jnfns85eZmsEiJjVMu6K8oFvMI59kZM=", "0YLQiBdFr8W6Rw0IZsPBLUGIpmAIAXFU+/fF8a7+qN8=", "G99cO2vLdYk1XnWBux+I/aDKGZEieXZMRrcr30BKXJI=", "PVCAlhpkUMUWTU6HqJKg1LIykragrrUTeDoyV9AjmrM=", "39axFwiQFkzokkiIenA27/ydnlbr+xE54GWJX74NnKc=", "ytLytIK0Gcl0OydlfuY/bZnYC420pDjLoTy5gmahIP4=", "XNHWwsSI3MaSzVyYHTk5vyyWiGWbzFBlLggT0zvvqWM=", "kq7784Y91ArIdbN4Jx91gOA2yARNUwsqQuCdn6pH/S0=", "To7CLv5Y8w9NjR4oZ86YemJ1CuglduZEtrUjr7gnGSo=", "NrcPLU8crdga5VId42FZ0vM5mLCWDLYmQ5ySakFAvfg=", "yFtPkJ0fKiBtOD9+rRsJtYYaz3xyc5GR1ujupSTt8uE=", "OyWpcVp2w7EvklfPTF3cF9C5p5DYDCgGhaStUZodRig=", "VwRNDC/X4bUDIfNWVUTdFjgeFBJJDLgF+9wzavwbiM0=", "2yDxlGT8vcVewmMeCjee92jNgoLiZ0QAGSbezPs4Tvk=", "AqR6pPNyXu9hhhghfwMDqpaxxd7r+phx/LyRIO1jN6A=", "0rh7NvKEWDa06F9UBes97f6D3oZF5TVHBqwEzBkrpGo=", "HpuoCwkDkXqWVCpjXCey2UCcRifgFN8pTU5zUeKt4Tg=", "eyPHzZ/4ezgiHqhFWIMtr/ckth9rvMr7UCg/IDF4gyY=", "vRSAdUZHmmbVTF626GSwwTC5atVtowLxKitGBk+8i0g=", "/T9bCHtxQzWby6YWLCkw9qao9Kli6vbkBNajOympOn0=", "fohMLRJbrWS5+2S3jYdFGmM4conQCkHVbNqZYwO+e54=", "cfwx9b9LvZ6o5iMTVRvDvWBVehfeQyJ0nKdRmaowDS4=", "qhjFxUbTyDSWOwaoaez0cGVCLQkckCncCT9MCEs9U4Y=", "04NoYgBJpXQKQSF0j/QVYBKlHJK4VCRFC2glGrO6RxU=", "xdSZDwvXlYo4eOCnFsPEFJNYQqfIS30m8+0i6CD9dow=", "QqrBFJ/KTny+quhKIHsGTT/sG9t7+ACBpZwwHlN7Pi0=", "9qciwRUId+XbLgCKUU9/aSXHebdtBi1cm7cZ/7LtXKI=", "N/YhGvP+CcBDgFrzR9qTkEtYsNKgfDKg0Nyzq/Wu1M8=", "he5NCQa/qbQuQJenbU9Wi5fzcHvAMGNWLvRAM2fulqo=", "FNbMANqnH55GpVfueSvxTPInokp1TlBmSg5WFgF03sQ=", "4jxfG8XNB5NR8gjgsHr1K2B/+iaFVOibyOqcIyUsZKo=", "OKsOiQIAuNBIUZQLGUPEsR+3P867EM+Mk69cw/80ZQg=", "LMquXsCKa/Eaj2ozbZ7Ipayy2nPTmVPMktg01Zjna/U=", "bU4lD98H4RzzQ0rENJriTa6QN5NtsCUb6AzPRPkEoB4=", "AakMzzYJGLzpuhlZtDCAGtxa1NTdV0oxbNkT82e2AFQ=", "P7Ver4buoCL9eYRermSNLnbJjztPZ8Dvj/GgE48hnMo=", "PS4F1O88PB7j30Qa0m0CeOzx7CMZG/0aAfa0uFfKcNs=", "RH4ayEkuY0yWVdfXoBOdaDzxOvmaZlbmToeYTCe1qnA=", "EA8LEgf4V95lFYCNSHTcoMujI8Qj346JY8SnR+Qtw+8=", "v0ohWcPZZxxwyFlG3KPIPMh39Fqy/0nClieIIxEXRgY=", "75MAETv7PO5KFY+7GieYe+CBDJ+BZFALK9aFH6DarAk=", "3iWmeyEzMt3v86fNNsvUsIoTsusxtwOAmHlmIEyPRVw=", "FDaP7p5Rra37BQW9LC8tNgbBaIdhIGuQKjLnJhRiazo=", "b7CA3i5vpTrdGIaMATwoQEcV7b3HTRtMgrzNj4/w/+4=", "RLOGXzHHCcYOIuPRS6BnovRKdY2SroDI5vflg0iusrc=", "bHmnjsezhUBXlE7n2KVuhAJMnH6AKUalK6iKgIyT6sE=", "OuHYWf3bFuXg9lMSLqDR3DJCFkRHulII0MurbjrPJj4=", "7pjfPEodtjmEL8TqzPzkP3m4X9Dzlh3YScRiBd1o8Ho=", "XwtE6rLIP+O4Byx7pMdMlb/a60RgQvGMTgFNWbNv/u4=", "GgqqXSUToRaWiWzTUGzBwMsj/07PyU0wkTduMULdAC4=", "5xuwgxymsAwSvD5c357vG9MYS3xQukq5anFLjXLST1A=", "se0N7wB6M9VZz07HvWkJEY/uSiyAoISxs2ZtjhySq+U=", "AP/NYLtOJBoT+nsmPGdJ2m8/pJopvj33zMxqZSKfv/U=", "DbmTBbHFsya6DU7Slt+25zPkg9P1zxMVxcppiL2zIkc=", "2JQkvXJ3p0dQ5z8yb4aBg9oMER7lJsFyhhiBTd4FOAA=", "kW0+iWXCN70duBE4Ar6psUpmuOn0hwmXEEjKGz/CJoQ=", "wDo9h6PRDm1sZdpUeaCUhf9yJzBVlW+2NHWkxeH41KY=", "hHxlWlm7p5vRDBR8Vs5vprrtu0qyc1iNo7urPBebpfA=", "NPUgf6TegTcJGUwhsmCZNbqW7WIJr0CkGBzqOiZ7oGU=", "+hfw3GGvqOI37RRb9/1YknGQFTvNJgHrjv5Lc101s6c=", "C9oUvTQ5ntIrGLKZEjSdVITOdVeuwwDwuapRoPLWShA=", "QWkDNb7QC0v9BqRTF9TsUBBBXjxZxdk730u3Cgqoz/0=", "z8Ra/VAUxcFj9VD6NQVMFIxOPmSQSA2+f9psSZdZPb4=", "vxJI4nynnTjFmGaMe7fKuvN2zKRlBrNMz6iKV8TGGls=", "/Se39ZKQHbgYDqlXM4Ea1FnsE1otIUHbgymfNBG1YBM=", "kuVc8XdIT+WmPltLWGG/ry+qyO1syyNUvRvDzlXn3oY=", "R/fp+bWWXLIiDSQzNbliK9uQlF/L+6wT4u8lOv51E+A=", "qRjGIwUjmuddFuJECIbJzGGs644PsEr0CUABGLvfhu8=", "IUt2Knn3mWqKyGepWUN5Hig+ROoPFNTZ6O82D4+w3OU=", "tDjVNwuhahPEz1nyHpSScA25Zi99QE/nl1fjbYk9pLA=", "/JsoDKiEtHdR4QKEiAzzbnuGy7yTpS2k/BEGhp+1uSI=", "+DMXL9akUtnEXLP+jXtOTYyxGPN3X+v2j5CzOQS/tQA=", "E4Rekinq8+MITKY1m6Rmm0a5WWMztSPoH1bMRUktJMY=", "T7gJMzuoBY91TvHuWT8SQl2e9iat+881nlm35giPWrU=", "GrJA2bDtMM65SSgAUV26uiwmcyiAdQqIiEMen1XmFzo=", "LHoss+7gTQW2jCyT+CjQyeVuQYYSLw0nvWSczK4DYH8=", "H31FdnWF+/YvQXG/7k7KW4b1d9IZDup0d07dzmvN2XU=", "osVB/Xyfcv1HDoTvrzHBiE1MqdqcBuuAPCImrFAfCx4=", "keOOPQwtlo443SE8lEwLh7c0PNJAdnBE45zfzRuUdSA="], "CachedAssets": {"4bLQJaiwON0JerB0MwbCfyaIVGyNWnrIJ+oQ40EjLmA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-05-29T08:04:38.0598944+00:00"}, "9ZTp7yMDzhBos0Mryf4Jag4ROIw5etyq6jheLyjBTV4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-05-29T08:04:38.0639795+00:00"}, "J95Hz6EwCT53xc5rDFs7R0OevgprkT7px3BjBfWU5s4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-05-29T08:04:38.0677314+00:00"}, "Z76PYICrt6T7dccGSEo3mWHYWD8dZRJ5OQL0jI2SWqY=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-05-29T08:04:38.0733485+00:00"}, "AukLs3gRQdmxvHCxeir32tO1mlWZOWOaup7dzV/bYPc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-05-29T08:04:38.076709+00:00"}, "r1KMRyNh24HHoma+sa0ONw6l91P36quUELrjbnMa+gw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-05-29T08:04:38.080024+00:00"}, "ln5KiVbcUlsR9hm3x517flNGKYPi4GehLEcdSoUpmtc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-05-29T08:04:38.0708324+00:00"}, "Frzii7wF1NnX3lq4qIRDiLc+QcD6n5rQCUfwZ96fr5M=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-05-29T08:04:38.0621174+00:00"}, "iMOdgBYVz7uSS4G9T6rOjsFluy2Qf11XvxKUZTGRVo0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-05-29T08:04:38.0656662+00:00"}, "XVYj7p7MHjI7nz6Ki54qX/uU6uJ4UQTwZE75w+x3XX0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-05-29T08:04:38.069739+00:00"}, "C+ZC4GDT+RARfZrLrf6czwG9ZbNx+JzdFYzxw2dthLM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-05-29T08:04:38.0708324+00:00"}, "ZC9AVXZ8xxkf7a252PZrFuQyCiNL59KdQgVTnep8PxY=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-05-29T08:04:38.0760478+00:00"}, "BfDyfSh0j+iqXDiXOwOszVXhpGsDtoWQQ782Zn9ypnU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-05-29T08:04:38.0787132+00:00"}, "d18UtfrN4r4YK8ceTkq75l3m/zyxWZdxI4hEeoX7FPg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-05-29T08:04:38.080024+00:00"}, "a3hfmyQE/tHFpxRGb3gj46H6hGT38bRxLBfswAPM+YY=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-05-29T08:04:38.0733485+00:00"}, "ltR7BG0mcWjnBLIVDF6yAdnT4k+k30O8VutpzR4sihg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-05-29T08:04:38.0787132+00:00"}, "7h/AJtYCdqPxjegpctb2XBdbyNg00ly528X2jZPrNoU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-05-29T08:04:38.0621174+00:00"}, "JUkhGSPwUXkPof7L9R7QZbCF/JlDu6DtunH7l7uY8Hw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-05-29T08:04:38.0677314+00:00"}, "j13r9Lj8PQuvl2O+ueEX6wSsacKA+WGGwmVyhWy919E=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-05-29T08:04:38.0733485+00:00"}, "Frw5yWra9OSq1YTkFgXL5+LiW7cPTHtdLNfuiK8t84Y=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-05-29T08:04:38.0733485+00:00"}, "q5CfsVlZv3xegktIn73eRNsX1AYI2dL/hO6qlFjtcbM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-05-29T08:04:38.080024+00:00"}, "Muwijw5oZuNcJzAZ7ytkjkE3lRsLGN+4epLMJZk4Sq8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-05-29T08:04:38.0827015+00:00"}, "NJrxBz01MEV4ZOOB1rBsECjR8E6IOj1e2AynK+hfJZg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-05-29T08:04:38.0887002+00:00"}, "UiJ4jXJf8jNN61wQlgTqfhwvwwm8Xcg6MeOZ4s/deqc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-05-29T08:04:38.076709+00:00"}, "nAzjFFXkS6gJXPyjDNU+dSvxbRidId/nzi2kr4znJRY=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-05-29T08:04:38.0827015+00:00"}, "UJ57z4qUd8lOv0lHTEuPr508177KfiQA0Ze70bLlup0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-05-29T08:04:38.0646352+00:00"}, "+2Zmb7YIcl+OJiLRYrTzhCvL374nUWBxgizCy/VftlM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-05-29T08:04:38.080024+00:00"}, "mHHuDFEc4yyEXhe/WWH5Ydag4kqYIfRio00LLGFTYbs=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-05-29T08:04:38.0847056+00:00"}, "3fPTp8cqYR5Cdk1NEmAROyHxTuWQx8VjOw8peA/xMk8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-05-29T08:04:38.0896743+00:00"}, "AY3+hsQbOsIdZ3REsrYSm3fv0Lr4aYS80Wizi3qgAG8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-05-29T08:04:38.0934419+00:00"}, "HVisagfB07jwh9n1jsX9Ne0X/954mI+qH02AKS5NjyQ=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-05-29T08:04:38.1045958+00:00"}, "8lQho+4TRFfF9jg52aK4O2jnsHmSb/acGyTX38idH2E=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-05-29T08:04:38.1074116+00:00"}, "HqyVhFy1k8mRBd+OpDUaL31M2sQQR7P5hzLgZxjNVmw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-05-29T08:04:38.1174403+00:00"}, "67rB9OqC6bCpuJrgQvhQe+RIjrojsiP2a42HFATBEAM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-05-29T08:04:38.1212457+00:00"}, "9HhaC0C3gx5/R87EvRa7WHpEKUX4MaBPIwCgpYfDHDw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-05-29T08:04:38.0733485+00:00"}, "nUxgCk/h6sZ38lenqSwcYIAFsgax6LSEXg8BvTum4wk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-05-29T08:04:38.0787132+00:00"}, "3T9c8yxSA0UvlvC1gvBoTZyd9tZtGx+gv+bbTUoBBG8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-05-29T08:04:38.0867097+00:00"}, "/hBflUHTvLlh3GA6SWEce9zR2PJgEEt2e7f2o20R42U=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-05-29T08:04:38.0847056+00:00"}, "snpU+40thaAzMSZXjH0dyn2iGzqSzIRP1SIkxTI/Xj8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-05-29T08:04:38.0934419+00:00"}, "TMo6vJW9s/325K7F75cqiAliFOY9vhWzOTq80qPrRmc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-05-29T08:04:38.0951165+00:00"}, "XnMb/ZJQ/z2fc5CB3bPKZ4i4pjorx045qrJhyN5z6lk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-05-29T08:04:38.1009473+00:00"}, "0WfMTM9WhkSCxEQr7ys1GdGfuanO0NwoKrjpU/jmoOk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-05-29T08:04:38.1045958+00:00"}, "mYv0c9NrrHdZb+oXGfuUv7mTcU6KMpA3VZiD6yQMbYU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-05-29T08:04:38.1114606+00:00"}, "EHtX+CEGvtO7wZTu0HU43JNI22Swf/F0NVP2O3hAF0k=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-05-29T08:04:38.0646352+00:00"}, "pZ6OJd/RgmtzDr+dZEB9Pux0SiTwDeKZb4CArtvsTz0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-05-29T08:04:38.0708324+00:00"}, "SvL49To5hamDjLBQMz+y5C0QDA0CV6BevDqYIq8MGWU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-05-29T08:04:38.076709+00:00"}, "QpfGJaym8viiliGHov1ZcJeUDTRmk6OxXuHL3VwwH5c=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-05-29T08:04:38.0787132+00:00"}, "gu11ADHUClY1PDkKPVsN6GCErrzuFbzsGFsUEvqvWTk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-05-29T08:04:38.080024+00:00"}, "SwnaE9OTiI8A23I//OtQcE7yqK6djbbNTdC6jdoZqe0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-05-29T08:04:38.0827015+00:00"}, "p7fnFz8z4cXGgMkgwcqA4dmBJqozjd6WnUInOvH4i1o=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-05-29T08:04:38.0867097+00:00"}, "j2a6wH3wJFhFPK5jq42R+2c6+3IIEHTVmmimNFTjMmk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-05-29T08:04:38.0847056+00:00"}, "edoHiozjQMF7Burp3K9lgLmHw6vzPmSJmVBfYjZEpMU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-05-29T08:04:38.0867097+00:00"}, "N/vbAf6HEm+TsxPxIsB8yCaaB6i03Z8032a2d8fVb44=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-05-29T08:04:38.0621174+00:00"}, "wN+ej3PTx+ZUfNJuaxPzhmOTlbJHc6KWQ7UTNT8RKug=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-05-29T08:04:38.0708324+00:00"}, "5A895m1V7FgRQbZ32c2Xjgcp/Qg0OMR09OzsskqRVWs=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-05-29T08:04:38.076709+00:00"}, "Bv6RRdLDcnXL/e66sguXNn6tzs3kcrMDjBB1s/quuDU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-05-29T08:04:38.0827015+00:00"}, "c7vOQQHtlzM/TOrZCof4Z6t2bjVCnJlkS06MSqVTCSk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-05-29T08:04:38.0904599+00:00"}, "+YDaKeHs78scTInUc/QK6sde8bl2YaGJSocud3dPFrw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-05-29T08:04:38.0934419+00:00"}, "2KLfvCXlq3A59AUo02LY5MBlKhnd2wvfSE8epiBbzyo=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-05-29T08:04:38.0983125+00:00"}, "AAGXh47nSk4X5sN7SreTC10YkSsqaJRYWFN7rDLig50=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-05-29T08:04:38.0847056+00:00"}, "H31FdnWF+/YvQXG/7k7KW4b1d9IZDup0d07dzmvN2XU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\5wyh8fqgul-5t79nrl680.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "css/custom#[.{fingerprint=5t79nrl680}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\css\\custom.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bvky1pda67", "Integrity": "2Wy/c1d/nf+zukK8rtE50mwDjUF7nPmC1Wn3dqFyE94=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\css\\custom.css", "FileLength": 3113, "LastWriteTime": "2025-05-29T08:51:13.8737143+00:00"}, "LHoss+7gTQW2jCyT+CjQyeVuQYYSLw0nvWSczK4DYH8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ueryi6faph-gm2o380hld.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "localization/ar#[.{fingerprint=gm2o380hld}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\localization\\ar.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pxdomnap16", "Integrity": "czGSHctXL+Wmbu5aW1BNOHtjwbfpx3GSeaRwS2VgQ4c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\localization\\ar.json", "FileLength": 1971, "LastWriteTime": "2025-05-29T08:04:38.0867097+00:00"}, "kq7784Y91ArIdbN4Jx91gOA2yARNUwsqQuCdn6pH/S0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\0wkx376cxg-61n19gt1b8.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-05-29T08:04:38.0656662+00:00"}, "bHmnjsezhUBXlE7n2KVuhAJMnH6AKUalK6iKgIyT6sE=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ifcnnv47is-iovd86k7lj.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-05-29T08:04:38.1045958+00:00"}, "heJH2a/YKpjXepmKsJq9m7S5sfjqFmyqSEaOaIrSo4o=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-05-29T08:04:38.065352+00:00"}, "b7CA3i5vpTrdGIaMATwoQEcV7b3HTRtMgrzNj4/w/+4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\l1ksyiojky-6pdc2jztkx.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-05-29T08:04:38.0943428+00:00"}, "OyWpcVp2w7EvklfPTF3cF9C5p5DYDCgGhaStUZodRig=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\4uo46uk8cw-c2jlpeoesf.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-05-29T08:04:38.0827015+00:00"}, "3iWmeyEzMt3v86fNNsvUsIoTsusxtwOAmHlmIEyPRVw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\vw7dhz31n8-ft3s53vfgj.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-05-29T08:04:38.076709+00:00"}, "2yDxlGT8vcVewmMeCjee92jNgoLiZ0QAGSbezPs4Tvk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\01flxr017q-aexeepp0ev.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-05-29T08:04:38.0621174+00:00"}, "v0ohWcPZZxxwyFlG3KPIPMh39Fqy/0nClieIIxEXRgY=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\1w6kpgm46c-hrwsygsryq.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-05-29T08:04:38.1025611+00:00"}, "0rh7NvKEWDa06F9UBes97f6D3oZF5TVHBqwEzBkrpGo=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\dj0tcf9tdp-ausgxo2sd3.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-05-29T08:04:38.0708324+00:00"}, "RH4ayEkuY0yWVdfXoBOdaDzxOvmaZlbmToeYTCe1qnA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\vhzpi13y9u-v0zj4ognzu.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-05-29T08:04:38.1035728+00:00"}, "HpuoCwkDkXqWVCpjXCey2UCcRifgFN8pTU5zUeKt4Tg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\g61qy9qscw-22vffe00uq.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=22vffe00uq}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mxqr4d2em4", "Integrity": "Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5976, "LastWriteTime": "2025-05-29T08:04:38.0760478+00:00"}, "P7Ver4buoCL9eYRermSNLnbJjztPZ8Dvj/GgE48hnMo=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\s66ds0h340-pj5nd1wqec.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-05-29T08:04:38.0904599+00:00"}, "vRSAdUZHmmbVTF626GSwwTC5atVtowLxKitGBk+8i0g=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\l7hecbek5s-qesaa3a1fm.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=qesaa3a1fm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dtwnk525ov", "Integrity": "H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3407, "LastWriteTime": "2025-05-29T08:04:38.080024+00:00"}, "bU4lD98H4RzzQ0rENJriTa6QN5NtsCUb6AzPRPkEoB4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\sucryzqgkr-nvvlpmu67g.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-05-29T08:04:38.0708324+00:00"}, "fohMLRJbrWS5+2S3jYdFGmM4conQCkHVbNqZYwO+e54=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\gixzetcklg-tmc1g35s3z.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=tmc1g35s3z}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7jljgo6o9r", "Integrity": "w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3217, "LastWriteTime": "2025-05-29T08:04:38.080024+00:00"}, "OKsOiQIAuNBIUZQLGUPEsR+3P867EM+Mk69cw/80ZQg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\9ahs3oxegm-j5mq2jizvt.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-05-29T08:04:38.0646352+00:00"}, "qhjFxUbTyDSWOwaoaez0cGVCLQkckCncCT9MCEs9U4Y=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\cqwfqr3kf0-rxsg74s51o.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rxsg74s51o}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xmcjt9e9cy", "Integrity": "ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3397, "LastWriteTime": "2025-05-29T08:04:38.0612725+00:00"}, "FNbMANqnH55GpVfueSvxTPInokp1TlBmSg5WFgF03sQ=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\sau7oekj11-c2oey78nd0.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-05-29T08:04:38.0887002+00:00"}, "xdSZDwvXlYo4eOCnFsPEFJNYQqfIS30m8+0i6CD9dow=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\zfzxseweal-q9ht133ko3.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=q9ht133ko3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5fijz6kmv0", "Integrity": "AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3250, "LastWriteTime": "2025-05-29T08:04:38.0677314+00:00"}, "N/YhGvP+CcBDgFrzR9qTkEtYsNKgfDKg0Nyzq/Wu1M8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\6dc0l2jxb7-r4e9w2rdcm.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-05-29T08:04:38.0827015+00:00"}, "9qciwRUId+XbLgCKUU9/aSXHebdtBi1cm7cZ/7LtXKI=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\uz2lyigxms-gye83jo8yx.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=gye83jo8yx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k64ed53csw", "Integrity": "VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12081, "LastWriteTime": "2025-05-29T08:04:38.076709+00:00"}, "QqrBFJ/KTny+quhKIHsGTT/sG9t7+ACBpZwwHlN7Pi0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\60h6ccmi8t-jd9uben2k1.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-05-29T08:04:38.0708324+00:00"}, "he5NCQa/qbQuQJenbU9Wi5fzcHvAMGNWLvRAM2fulqo=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\224ghrl0aq-wl58j5mj3v.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=wl58j5mj3v}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nv0k9gv8u2", "Integrity": "cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11067, "LastWriteTime": "2025-05-29T08:04:38.0847056+00:00"}, "04NoYgBJpXQKQSF0j/QVYBKlHJK4VCRFC2glGrO6RxU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\sfyrk4wm38-ee0r1s7dh0.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-05-29T08:04:38.0656662+00:00"}, "4jxfG8XNB5NR8gjgsHr1K2B/+iaFVOibyOqcIyUsZKo=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\c35cgxkat3-d4r6k3f320.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=d4r6k3f320}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42ih0tfzwc", "Integrity": "rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12033, "LastWriteTime": "2025-05-29T08:04:38.0914376+00:00"}, "cfwx9b9LvZ6o5iMTVRvDvWBVehfeQyJ0nKdRmaowDS4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\zde3zg7pik-fsbi9cje9m.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-05-29T08:04:38.0847056+00:00"}, "LMquXsCKa/Eaj2ozbZ7Ipayy2nPTmVPMktg01Zjna/U=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\695e9z8lx3-keugtjm085.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=keugtjm085}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q8bg8bu1pt", "Integrity": "Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11050, "LastWriteTime": "2025-05-29T08:04:38.0677314+00:00"}, "/T9bCHtxQzWby6YWLCkw9qao9Kli6vbkBNajOympOn0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\gthtcsx6jh-fvhpjtyr6v.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-05-29T08:04:38.0847056+00:00"}, "AakMzzYJGLzpuhlZtDCAGtxa1NTdV0oxbNkT82e2AFQ=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\xukadz714f-zub09dkrxp.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=zub09dkrxp}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nqh38a62u8", "Integrity": "j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33518, "LastWriteTime": "2025-05-29T08:04:38.076709+00:00"}, "eyPHzZ/4ezgiHqhFWIMtr/ckth9rvMr7UCg/IDF4gyY=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\qc852yxj2z-cosvhxvwiu.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-05-29T08:04:38.0787132+00:00"}, "PS4F1O88PB7j30Qa0m0CeOzx7CMZG/0aAfa0uFfKcNs=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\7gi0sn68zr-43atpzeawx.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=43atpzeawx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cmcmkw9yu7", "Integrity": "ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30968, "LastWriteTime": "2025-05-29T08:04:38.0943428+00:00"}, "0u0M3UJGwlKn3nnWZoHBf6oPUgvxmVmqvMpmRBxfKYI=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-05-29T08:04:38.0612725+00:00"}, "EA8LEgf4V95lFYCNSHTcoMujI8Qj346JY8SnR+Qtw+8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\jhlg3z209o-ynyaa8k90p.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=ynyaa8k90p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0uw774o5yt", "Integrity": "CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33374, "LastWriteTime": "2025-05-29T08:04:38.0904599+00:00"}, "AqR6pPNyXu9hhhghfwMDqpaxxd7r+phx/LyRIO1jN6A=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\g4pes73y9h-xvp3kq03qx.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=xvp3kq03qx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1xcrflemqm", "Integrity": "Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6755, "LastWriteTime": "2025-05-29T08:04:38.0656662+00:00"}, "75MAETv7PO5KFY+7GieYe+CBDJ+BZFALK9aFH6DarAk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\bx5gql4yz4-c63t5i9ira.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=c63t5i9ira}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n2nwmiwapx", "Integrity": "zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30991, "LastWriteTime": "2025-05-29T08:04:38.0621174+00:00"}, "VwRNDC/X4bUDIfNWVUTdFjgeFBJJDLgF+9wzavwbiM0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\snnbr3djt1-sejl45xvog.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=sejl45xvog}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mhey2fgxm6", "Integrity": "2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5973, "LastWriteTime": "2025-05-29T08:04:38.0847056+00:00"}, "FDaP7p5Rra37BQW9LC8tNgbBaIdhIGuQKjLnJhRiazo=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\xsbbsidfg6-4094rpi4f9.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=4094rpi4f9}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u3mr700haq", "Integrity": "qk/LGv3UCKBZhVKSwN0Lvyprj8TiWmp+iiWhP0Dwdt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44640, "LastWriteTime": "2025-05-29T08:04:38.0827015+00:00"}, "yFtPkJ0fKiBtOD9+rRsJtYYaz3xyc5GR1ujupSTt8uE=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\9lzoulic0o-t1cqhe9u97.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=t1cqhe9u97}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7trewrvtze", "Integrity": "td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6754, "LastWriteTime": "2025-05-29T08:04:38.076709+00:00"}, "RLOGXzHHCcYOIuPRS6BnovRKdY2SroDI5vflg0iusrc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ivo05jppsk-hd3gran6i8.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=hd3gran6i8}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yc8010sx1o", "Integrity": "rjTJ9Ut8h6OLoNYsNgr7e/TIritk83K5bucjnu0BPlM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23992, "LastWriteTime": "2025-05-29T08:04:38.0951165+00:00"}, "NrcPLU8crdga5VId42FZ0vM5mLCWDLYmQ5ySakFAvfg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\yng81s5emu-78x9uzmf1f.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "js/site#[.{fingerprint=78x9uzmf1f}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mi026k8bll", "Integrity": "4rrRkWoK7cek8Jn8U7K6vFWy9C5pQcPyEbkfL/QBlYA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\js\\site.js", "FileLength": 393, "LastWriteTime": "2025-05-29T08:04:38.0733485+00:00"}, "OuHYWf3bFuXg9lMSLqDR3DJCFkRHulII0MurbjrPJj4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\j1rtvjdsvi-ltid2c489k.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=ltid2c489k}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jgdip1sczm", "Integrity": "ozEUTWBbc3wKZYb3ecLdk+9yMj3kbTTBJx6aTKh2FDs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 29043, "LastWriteTime": "2025-05-29T08:04:38.1084247+00:00"}, "To7CLv5Y8w9NjR4oZ86YemJ1CuglduZEtrUjr7gnGSo=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\2hjkq69mky-34bjharz5s.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "js/dashboard-charts#[.{fingerprint=34bjharz5s}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\js\\dashboard-charts.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h5rk824j3r", "Integrity": "dRbbiCEnhkcIxyEtKypO+f5B8pikdq+fMMqzvvvHkWY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\js\\dashboard-charts.js", "FileLength": 1372, "LastWriteTime": "2025-05-29T08:04:38.069739+00:00"}, "XwtE6rLIP+O4Byx7pMdMlb/a60RgQvGMTgFNWbNv/u4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\grxx96oju3-8vyfqsqgz1.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=8vyfqsqgz1}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0v96h8eab3", "Integrity": "bKh1MfaBAldvWxNRIEd7ZCFzOhTySMaAYostY/K7XbA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18643, "LastWriteTime": "2025-05-29T08:04:38.0951165+00:00"}, "XNHWwsSI3MaSzVyYHTk5vyyWiGWbzFBlLggT0zvvqWM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\czdec1f1i9-reqyzuf3o7.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/js/datatable.min#[.{fingerprint=reqyzuf3o7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2j7vh1caua", "Integrity": "IjPifJJIaIyYR1EDWQM+sE1EtIwmWTA4A3vTAftn7PU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.min.js", "FileLength": 6015, "LastWriteTime": "2025-05-29T08:04:38.065352+00:00"}, "5xuwgxymsAwSvD5c357vG9MYS3xQukq5anFLjXLST1A=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\m5d864i7bj-u9q1upor1n.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=u9q1upor1n}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f711a7kh87", "Integrity": "pf2AOjAOccWBEl9ORfBaaHo0t3lQUfisiqfIeMW9tqI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29774, "LastWriteTime": "2025-05-29T08:04:38.0708324+00:00"}, "ytLytIK0Gcl0OydlfuY/bZnYC420pDjLoTy5gmahIP4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ok5a1ddg8e-e76ncinqlp.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/js/datatable#[.{fingerprint=e76ncinqlp}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72ex96jm7c", "Integrity": "kQkzmckB+YbyfT491HVgWjD50eEw1QbaAK4awOKnrfc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.js", "FileLength": 11002, "LastWriteTime": "2025-05-29T08:04:38.0621174+00:00"}, "AP/NYLtOJBoT+nsmPGdJ2m8/pJopvj33zMxqZSKfv/U=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\7j1wwkgjuv-4d85u1mtcx.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=4d85u1mtcx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "47nw1ac886", "Integrity": "cAYCo2Q8Ith/zqZVNFuI/cRoMoGlmld9t1DuyUGILPk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16642, "LastWriteTime": "2025-05-29T08:04:38.0847056+00:00"}, "39axFwiQFkzokkiIenA27/ydnlbr+xE54GWJX74NnKc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\jk0mq5gmrl-r6y55mx65a.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/js/datatable.jquery.min#[.{fingerprint=r6y55mx65a}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u7z45wkmzb", "Integrity": "SvCNF3LL4mgVJtreJAu13Z58W1Ul3jL5YC5Qbh+yfnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.jquery.min.js", "FileLength": 625, "LastWriteTime": "2025-05-29T08:04:38.080024+00:00"}, "PVCAlhpkUMUWTU6HqJKg1LIykragrrUTeDoyV9AjmrM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\mohobw47eg-zzb78u50n5.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/js/datatable.jquery#[.{fingerprint=zzb78u50n5}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ltu4apqqj8", "Integrity": "9xx1ifaCHtfStd6WqcTVHQiNb4D18mEkTr5lj7CnnAw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.jquery.js", "FileLength": 857, "LastWriteTime": "2025-05-29T08:04:38.076709+00:00"}, "hHxlWlm7p5vRDBR8Vs5vprrtu0qyc1iNo7urPBebpfA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\7pnd25z6x0-48y08845bh.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=48y08845bh}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5hgi50qbud", "Integrity": "F1eM1vgG9PaT7pF5Hubo7swbaVAWhXsjYwxoJvyk5+s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2206, "LastWriteTime": "2025-05-29T08:04:38.0896743+00:00"}, "NPUgf6TegTcJGUwhsmCZNbqW7WIJr0CkGBzqOiZ7oGU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\niscljinu6-356vix0kms.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-05-29T08:04:38.0598944+00:00"}, "+hfw3GGvqOI37RRb9/1YknGQFTvNJgHrjv5Lc101s6c=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\caqhplwkbu-83jwlth58m.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-05-29T08:04:38.0621174+00:00"}, "G99cO2vLdYk1XnWBux+I/aDKGZEieXZMRrcr30BKXJI=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ccb3zsii90-gmktcc4tyv.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/css/datatable.min#[.{fingerprint=gmktcc4tyv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rizmv1579y", "Integrity": "U5AFoUXYWOs6Upnp74FTJfOc/Mest39PWqN5Sy7rQo4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable.min.css", "FileLength": 286, "LastWriteTime": "2025-05-29T08:04:38.0760478+00:00"}, "0YLQiBdFr8W6Rw0IZsPBLUGIpmAIAXFU+/fF8a7+qN8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\wac810xtcm-c0yflasb9m.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/css/datatable#[.{fingerprint=c0yflasb9m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1auxemu7qt", "Integrity": "iZ3evWqMT35yQOTrfHLLoD/sfXY2hdNBpONkTQ+udOE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable.css", "FileLength": 377, "LastWriteTime": "2025-05-29T08:04:38.0733485+00:00"}, "z8Ra/VAUxcFj9VD6NQVMFIxOPmSQSA2+f9psSZdZPb4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\sjuinx3x68-gk0pw8i4co.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=gk0pw8i4co}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q07w49iitf", "Integrity": "itOFoWDZSqZIyZRKcH5nH/mbWKG8zHsAz7G0W9zjagg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8126, "LastWriteTime": "2025-05-29T08:04:38.0708324+00:00"}, "Pttxh6STR7O8Jnfns85eZmsEiJjVMu6K8oFvMI59kZM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\1cx5ci8a2y-298x6fratd.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/css/datatable-bootstrap.min#[.{fingerprint=298x6fratd}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable-bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glkolfxhns", "Integrity": "RJ9FhH7h9xR6LgrrGEdxY3imB7N1C3Im5nfONVUl550=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable-bootstrap.min.css", "FileLength": 166, "LastWriteTime": "2025-05-29T08:04:38.069739+00:00"}, "6IK+/8eIUn4rpMP5Zh06VksNjX4u7oMBDBYWYq1XNnM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\0rzj6l2fpt-t27oiczu7f.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/css/datatable-bootstrap#[.{fingerprint=t27oiczu7f}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable-bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pletbzcagi", "Integrity": "03kh08q3wnXtgu/dTeKxHcQfRk5NSjgZ319uVtvi2DQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable-bootstrap.css", "FileLength": 190, "LastWriteTime": "2025-05-29T08:04:38.0677314+00:00"}, "kuVc8XdIT+WmPltLWGG/ry+qyO1syyNUvRvDzlXn3oY=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\1qqyhyvf0n-m9qm4tazc0.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=m9qm4tazc0}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1s03qn3l9f", "Integrity": "T1Mxz6b5tcPGdGYeBLTL5A9PUSvbDbkClvy0G7J1r2k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30691, "LastWriteTime": "2025-05-29T08:04:38.0881754+00:00"}, "6NlJHO9/TB3YreywaJAEqdyfN5X0dE13M2aMgj2saXU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\21q1iv0nyk-0dyv18pzof.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "css/site#[.{fingerprint=0dyv18pzof}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ctbvroxfu7", "Integrity": "cIcFRtvlXxaej2eU/DvCTvqsuyOL575jT0wJxzqDOf8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\css\\site.css", "FileLength": 1468, "LastWriteTime": "2025-05-29T08:04:38.0656662+00:00"}, "4EOF3DQXr9WlqvRXOB12ERlUlsc66354HyzGVInCEZk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\hw370xkdza-dvvvkbu0fz.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "css/sidebar#[.{fingerprint=dvvvkbu0fz}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\css\\sidebar.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0f1yjnmmaf", "Integrity": "rMouX0jEywwQ08BIxHNj6g0204jcTTXHbec+8+QvRGY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\css\\sidebar.css", "FileLength": 987, "LastWriteTime": "2025-05-29T08:04:38.0621174+00:00"}, "IUt2Knn3mWqKyGepWUN5Hig+ROoPFNTZ6O82D4+w3OU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\g8lpotsu2c-9vr69e8ynj.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=9vr69e8ynj}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mulfwnbtpa", "Integrity": "oheIXejBlri6EJgnT3rCEOV8cSc0XnYutP+KQ6pSvDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24363, "LastWriteTime": "2025-05-29T08:04:38.0708324+00:00"}, "tDjVNwuhahPEz1nyHpSScA25Zi99QE/nl1fjbYk9pLA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\e3mlaesewq-87fc7y1x7t.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-05-29T08:04:38.076709+00:00"}, "osVB/Xyfcv1HDoTvrzHBiE1MqdqcBuuAPCImrFAfCx4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\58sgrd8jrz-e3r9dq85uh.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Computed", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "TajneedApp#[.{fingerprint=e3r9dq85uh}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\obj\\Debug\\net9.0\\scopedcss\\bundle\\TajneedApp.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "byhqz68cqx", "Integrity": "YziljzVs3JkEwclHWXkFlBxkqVMRUV0kkLrKVpNNeKc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\obj\\Debug\\net9.0\\scopedcss\\bundle\\TajneedApp.styles.css", "FileLength": 540, "LastWriteTime": "2025-05-29T08:04:38.0598944+00:00"}, "keOOPQwtlo443SE8lEwLh7c0PNJAdnBE45zfzRuUdSA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\x3xne9n2zz-e3r9dq85uh.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Computed", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "TajneedApp#[.{fingerprint=e3r9dq85uh}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\TajneedApp.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "byhqz68cqx", "Integrity": "YziljzVs3JkEwclHWXkFlBxkqVMRUV0kkLrKVpNNeKc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\TajneedApp.bundle.scp.css", "FileLength": 540, "LastWriteTime": "2025-05-29T08:04:38.0612725+00:00"}, "Os77d/sdhKJaVvgd1tz2yrJKB/DPZm2x+zZNDa8mCqA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\um3zez7ymr-dwmw1nforb.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "css/dashboard#[.{fingerprint=dwmw1nforb}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\css\\dashboard.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pgmlqv6tiq", "Integrity": "+TqlXBEnAMgS9iL6/MbQnYKKAwlQm1a3wb6F5IoZ9Ng=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\css\\dashboard.css", "FileLength": 621, "LastWriteTime": "2025-05-29T08:04:38.0598944+00:00"}, "7pjfPEodtjmEL8TqzPzkP3m4X9Dzlh3YScRiBd1o8Ho=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\0qu2fmqnfk-kbrnm935zg.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-05-29T08:04:38.0934419+00:00"}, "GgqqXSUToRaWiWzTUGzBwMsj/07PyU0wkTduMULdAC4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ahu6m88gcn-y7v9cxd14o.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-05-29T08:04:38.0656662+00:00"}, "se0N7wB6M9VZz07HvWkJEY/uSiyAoISxs2ZtjhySq+U=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\yp8pnxzlnp-h1s4sie4z3.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-05-29T08:04:38.080024+00:00"}, "DbmTBbHFsya6DU7Slt+25zPkg9P1zxMVxcppiL2zIkc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\4up49nv7a9-0j3bgjxly4.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-05-29T08:04:38.0896743+00:00"}, "2JQkvXJ3p0dQ5z8yb4aBg9oMER7lJsFyhhiBTd4FOAA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\hdkhr08g7s-e8mwk64ned.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/css/datatables.bootstrap5#[.{fingerprint=e8mwk64ned}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\css\\datatables.bootstrap5.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "49dyq1i500", "Integrity": "mNWTD1S0Jw8vfxuCPG4kuNNOca5Fq0n3J+BlhI6tMqU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\css\\datatables.bootstrap5.css", "FileLength": 2636, "LastWriteTime": "2025-05-29T08:04:38.0914376+00:00"}, "kW0+iWXCN70duBE4Ar6psUpmuOn0hwmXEEjKGz/CJoQ=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\5py4bhyr65-3gk2gkf8pu.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/css/datatables.bootstrap5.min#[.{fingerprint=3gk2gkf8pu}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\css\\datatables.bootstrap5.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bn2wubp9h3", "Integrity": "Z4+inWfKzD1Dlb4iN/2ZIvv4tnR66/VbLx4WrGpPQ64=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\css\\datatables.bootstrap5.min.css", "FileLength": 2530, "LastWriteTime": "2025-05-29T08:04:38.0914376+00:00"}, "wDo9h6PRDm1sZdpUeaCUhf9yJzBVlW+2NHWkxeH41KY=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\z5bysyzta7-03hjc8e09r.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=03hjc8e09r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hj6zx16vtq", "Integrity": "I28FOsKinIPvKQJYJhPmeb5aWTZaShTculcMW3WSRdU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4679, "LastWriteTime": "2025-05-29T08:04:38.0887002+00:00"}, "C9oUvTQ5ntIrGLKZEjSdVITOdVeuwwDwuapRoPLWShA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\j09i2ri0iu-mrlpezrjn3.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-05-29T08:04:38.0646352+00:00"}, "QWkDNb7QC0v9BqRTF9TsUBBBXjxZxdk730u3Cgqoz/0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\5lcpkibyr6-worgj0nhwm.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=worgj0nhwm}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xj2ze9hdow", "Integrity": "rdkQBPBwlP4JzOj6r9lyBjOEX/yZn2usvoAwy9AWc1Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14178, "LastWriteTime": "2025-05-29T08:04:38.0677314+00:00"}, "vxJI4nynnTjFmGaMe7fKuvN2zKRlBrNMz6iKV8TGGls=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\19jwi9s4g1-x0q3zqp4vz.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-05-29T08:04:38.0733485+00:00"}, "/Se39ZKQHbgYDqlXM4Ea1FnsE1otIUHbgymfNBG1YBM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\dz62sxilx1-d6nrnj6jjx.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=d6nrnj6jjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2k5ha5qn83", "Integrity": "nuuauMZg92Lq1w9JnCHRfNGUNc9WeRQGkGwvzcHEyTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 85369, "LastWriteTime": "2025-05-29T08:04:38.0847056+00:00"}, "R/fp+bWWXLIiDSQzNbliK9uQlF/L+6wT4u8lOv51E+A=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\z5ri8hcg1i-ttgo8qnofa.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-05-29T08:04:38.0914376+00:00"}, "qRjGIwUjmuddFuJECIbJzGGs644PsEr0CUABGLvfhu8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\vb1dc7l55k-jjsuc0puko.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=jjsuc0puko}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m8w5b0wq9e", "Integrity": "imJ8RM7oNfakmMXAji+mGEXDQYSPHKMWoVjRycEkdZ0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 69352, "LastWriteTime": "2025-05-29T08:04:38.0656662+00:00"}, "/JsoDKiEtHdR4QKEiAzzbnuGy7yTpS2k/BEGhp+1uSI=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\pv4pwfjqd5-mlv21k5csn.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-05-29T08:04:38.076709+00:00"}, "+DMXL9akUtnEXLP+jXtOTYyxGPN3X+v2j5CzOQS/tQA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\mo3atkfexb-fjtml3ocfn.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/js/datatables.bootstrap5#[.{fingerprint=fjtml3ocfn}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jd149db236", "Integrity": "9wzh2z56neBBaeXMTvFdGZ2EjZl5AI1ifVLkhh+OZec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.js", "FileLength": 1178, "LastWriteTime": "2025-05-29T08:04:38.080024+00:00"}, "E4Rekinq8+MITKY1m6Rmm0a5WWMztSPoH1bMRUktJMY=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\4as9pmjux4-vp752pwqcj.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/js/datatables.bootstrap5.min#[.{fingerprint=vp752pwqcj}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3wpu9fsym2", "Integrity": "k2PE+9yJ344ibWctZCtgF2A2r1609Z62nZcJ9U86d4o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.min.js", "FileLength": 743, "LastWriteTime": "2025-05-29T08:04:38.080024+00:00"}, "T7gJMzuoBY91TvHuWT8SQl2e9iat+881nlm35giPWrU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\rhymscq3ko-38jzjlrhc3.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/js/datatables.bootstrap5.min#[.{fingerprint=38jzjlrhc3}]?.mjs.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.min.mjs", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hh4wv7jzfw", "Integrity": "tcnrinJY3UtCDsoBflJED/eENZeg6ezxfddyl42Z+hI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.min.mjs", "FileLength": 587, "LastWriteTime": "2025-05-29T08:04:38.0827015+00:00"}, "GrJA2bDtMM65SSgAUV26uiwmcyiAdQqIiEMen1XmFzo=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\j8k8rx23td-yc14yep589.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/js/datatables.bootstrap5#[.{fingerprint=yc14yep589}]?.mjs.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.mjs", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8iep3heagz", "Integrity": "sDyxd6KmLh0PGSK/ojk2V28300rkcMcTZAM6gkkHv10=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.mjs", "FileLength": 853, "LastWriteTime": "2025-05-29T08:04:38.0847056+00:00"}}, "CachedCopyCandidates": {}}