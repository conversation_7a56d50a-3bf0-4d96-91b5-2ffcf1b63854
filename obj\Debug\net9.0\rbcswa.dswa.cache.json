{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["CdvuOY15cc6LcQ0uuT5LgfdHI7QW6A6cuAfszfoDTbs=", "mCZLZntD+5BBNkwM/mG6AE6+t6KI9tbVKtyM4GmENMU=", "PgOARTXdqvf5ocS5KY/omtOY9zN7u8HqWHKEIUT60wM=", "jaJHSpWfVNBI13hCjieGyqiz3ClVqqAGiin4HjmdOOw=", "MBpry5QQ204jFFun3qgcL7KP4G4uO8YNqooRWdRkPqA=", "wK7crylrKijUmCmnmMjlbZMoW7CuLhYhunUXhR8A7hA=", "kt+qIJZ/1f2IWL78300Vqtfd0ws3fUECmpFv1RnYICM=", "UbHMvTh12Af6hn62BiQrM1SRLwzaKFLGhK+2CUs/W6w=", "yRpdHjChkc+H33BTD4/iJ9Ah0e5PojDavurwIi56qvM=", "ep2qditTFDN7q+8ws33qSgLmocMWJq2jgRYbCloOGoA=", "7RaGvse+Nsf7AThgj25/BEHGmSigCSjCzOL4m22S5nc=", "YqDMAKhd+rkQO91XpNbeM04jcxcRClR4LJCJryPLPQg=", "CfBukD50bstg+4g9U1v5s4pVz0HzKmwMEVnskogQXjg=", "3oh14yhJh5cM40XqVDbrCjzh0r8d2/NqjTK9uL5edMA=", "/CrRW+yA8Yh9Va6r5qSO6frhspC12STXV94/ocKPeao=", "eqobdvid3iKTYLdiLMhQ1HMdNbNeHvXll65UoetZKt0=", "kkiBwAFp7c921+DgtXwQQ+0E8cf4NQpXPh2dO419EQo=", "Y5n3GM5DN3qaB0NvpOo11+oec5ZjIYMc03gpPb91260=", "q3t0th6aUesY3XrCrVAL1nKjf6ddKdKevaxej6NvVHk=", "cwcN+es/Fu6yn4oVATAhlHXwU+VQf9I414ceCGiiqak=", "stVp+bYgsYKoakW8NMMRvM+YLtcZ4AUg4v/U/9CkXoo=", "mknBfoluqwN1c1hrDDggUyFK9QoPOffSUGbn3GixHKU=", "hgRdAdYZCGZ5aIPFNNL02PtGB+3T5+7UMyYGGXNg5HE=", "GYG98W0fXFTtZkUqdq5tvczrlvvcnO0avCO7P5Mfu30=", "mDSwzn4Mq9O0XSeZxa7x46D6j8sIyR5NwTTd6OUDMYs=", "NCUChOuSDm16yvf2gp/rDafRBvgiPuoy0VLsKXvOkq0=", "bL/ewdInZCj94pwaH1ZOD4Ea9W1ubYBj6TSZ22AtR0k=", "SbylYTytt8siB04WU8vXnu5m9tJz33GEgp+cFyr3SSc=", "sqAXIpFzO0p3AZhnCq3cXedKzGAac4CpU1Xc721AIR0=", "cLcIMclCrtzLPYkhmkduDASOUulpvGoCZjCN0MFhDiA=", "A/twmKlk9CofUGja9/y5oLxhsjBMUU3bSvzzHcPgVEs=", "VurvlpAmn+Je5LfHdhcmbeL1nVE73jgK9eOvBJBusPg=", "YeRfAKZLtXVdL+3YaO80WC+Q6iBi/vkW3RrskrF5F2s=", "WLc0XLZebDkav8O7367sV9zBzrJDRS85UfooePwTIRU=", "vu2qEBgFTMwMhzAYhKI65kT7OiaLRdMQCR6Dzq7RJzI=", "A4kiEaJC8Fd0Ia4QjUihnTVYPyA/ZrVnYvYex3mXnhU=", "hrV0K0wuRt/hXvWuZx6cNOuDKz0vtmzG3n62GK9spoQ=", "JMLbg6hz2zFYHZONHXokfOjSx4MkPYyVNTzf1grY2fQ=", "52u1Q3R3xnGuLBLOY+HHLax4fuOaxACu++DYDGGac4c=", "cBxz5xXiJOXXM7y+M5ZCjn7PYObGYN4yiNjOpLKi+e8=", "gjedseVVrWwgN8j5qkgm5mgD+zOV2GGEuxsYWSvyEFw=", "eFEy1mSCz8swvXc65Xl3L8RujoB4wrnGfzVpE3mJU60=", "eY+skmEETWtxSm3wlEj5Rkc1QKcb84k31gZjud/nJHU=", "0/YuKijMBsnKGvk2ErPC1+1Rag09V8OQxz9nhLHND9E=", "oD/Jkm6bftYPrHxxAYyreLKlF/f755GkYQhjj/A7SmQ=", "ZrnQEDQGQ35efGH9yA1HMA3DNWKfFnnoIh9SyMOnUQc=", "WUPaSsH5xBeawlFOzNWDzJOSm2kY/453T6XRqjMCoDM=", "M7hVsbHWh05EfNyshwD+K1LeqxPfu83XrjX4hbmFjLw=", "twfVFPYWSKRToqnDQ7gep5bTKANsZzs2iZIubZrRxK0=", "pKJRl95iMVZDPxNv4T7oEOJFFtjxGDNTocbsl9fPxBI=", "JrKtF9F/JtlZFxLsOY8qKaE48EgOPvZYRzNNA1uoySg=", "lWs7kAVBG+TjP4MMv35DQ7K7qXHJ4sJlsXmIfHxFGrk=", "06Su6gSdGDrSvGArTFTFbm58dIiPDvPfGrl0dLt8i7M=", "0UoYawkV2PFQQ5p4X+bc7jsUlzdsBA4RDw6gdYN8KMk=", "SwZryww/2IMUUAeRvji+TNz0URg5ZSIrnTzfl5QofvY=", "4Sf6TGrOSnXFrPYldxMFvS8NStqQla2D+4mH/yvSRjs=", "0BWVCAzNEaQdYRlG/exz60CaN8IJsaUc/2UmaZU2Qxc=", "ULgop/Djmjnk5c6l1AfXh7z18e+2vMa+3cLy63OmzAQ=", "vB6O7p4siGaTvivXbEjDEqo/R0WYHIyAbAOgYw8aeTQ=", "+yg+iF6GI2njBZXAzcxkmfUz/m6qZpHv1Q+NK9NPnE4=", "GwI1LHkS+k2L3zCoYBhWDuM+2nF+Grz6V60tvYxIaIg=", "NxBJTQkNs1SD7uc40EeiVHFU03JJCgW3tDGcmWKoo38=", "rvx0scU97oxQWhu/aynFOsXNzzpesuJP+5oX+vAwDjc=", "9GT19f6PtxgCEa1LIYuLa7cHr08TQXQwX1oubn9rN0A=", "SAwBC2SbtpIAwMPCllm573ghV5xC9T/iawN3Gfg66dw=", "cyngyNoCNk+6APjoX7YSqDGQFvBtiUKNetgXt3RdhHw=", "hahJllGQp7jaaRKZplhTX92dO56eBXaZVh5iWWRol6A=", "njmFt58KBI6WjiMHJTniHc69QrRckgZ5jzfbg+kOH+o=", "f8rwNAFfe27IJbHX+FBDM+kmixEelGpiuy/870E9qWc=", "HQ47DLCWOKLY8/XhQhYBAfZpw9MjM8TuWDTWBdxZN2c=", "DIXtw+LCWsT1RjtrsxQSwcXLgfK7QNuEen4b4QWNweE=", "j/y8vDxl2/3X2Xz/0KgYsSIzjXboY45knME2J3mVctU=", "7FPF+LpE1QN+jjVy6GLge8WUtrSQL4EMe7vqJVnOSp4=", "JuScLr0KUG2kSbNgkrAk4sEgbqiiC9RIXcxvh9VnYF4=", "NIpQK5GwhQKDiLYCMt/98NXPumLhVBuQbtzaHClh0Ks=", "5AS2haTeJL9mJ+YSUFwN4deNLwefUikrEw3t1PC6hpc=", "47gSoLXagNZjBcot69fu7lV8VJ/KUjZKY7fJRp0Ej2g=", "vDCgyf5SPAUNGM6BloDLQHvV0yU6NcWDn+OIzeO/kMs=", "ZTqZ8E+MLAmYbjQ7joDsdJhhuaLR6ldeswN1tRgi1Vc=", "mI1qu8+zRFbwmWWlyCOdIs5yZ/VoNtkGM3Hwqd+ddaE=", "w6jQWkFcgJXHfDt0OSlPekGX8RhPbVt4y2+oUvajJho=", "bnlx8XEt9V6JNHiwLs6WCvDjF1CJYXAd4Cji/h7Y2lE=", "GQW+b2EBWppcCNGU4vXERJNPpEQvP17D02mimXxJu7E=", "MOTVvzzuYtkqUHPs4dEnB2gq0iUXELsjMyn7ij+CLyQ=", "g3r85+tiFuakGdNpW5ug7E/Dg4g6Tg1DDAvCo8RTHe4=", "xZ0vEpUiypCHXjtlRQUkVnJnqlxftn1LTwjLsU8KLKk=", "6NwSLArDwfW3vn0YEqJsr7gIx9O0Ed6hGV50R6nEpvU=", "5ebkHCXi6BdYht9h2z49wcXORUJaoyehsqJ/sDRaSNA=", "jdfKRlDv5I+CKL94fJQ+RRC9hJNl5mHAP5447kUVVfo=", "vkEeM9h9dYZSTEGd1LMu2g6qulwyE00+Og52WMYKh6Q=", "W6mYqW6t7O9QEtJXLy4eE9hcBYii7t3AsCPge8eN0WU=", "bAWkyBAHQir18iI9of2ZvKImlOQaWdnMdf+a67NAqDo=", "+V6BDRmweiUk+e1pWI63DoIuzVje2XKPS2XSJqXrXDw=", "4FPhWcf+HBSe77kWhCIbPU4SrvgQYawk55crblER4As=", "6yxGfggJZhdxzacEMZH7QfDGVQ3HBDnTZsZrUR1muO8=", "AuLHnvOfddrN7RcHT2EbaHcfiSg19VXFo3RCBDWOPuI=", "Na3eGxe2vNCfeTERy9tWMRTqI5eU22BLwQndSxjtVfA=", "IA4wUZig+a980dEj9z50MtL28v3AqbdGDyz0+xJS/ac=", "PcvJUISzN/RgY+Noz4qljeV5WC+PxivQMDIrcENqNj4=", "cV2C23PvXDzvOcIdZyudET/9S+XhVbOpQaGvz5FC7ls=", "4RwrHFICq8IKQnGtCBeqCv/cQEY43BH8W5d8sPLzp7Q=", "uinZvGV4Hwree+sH6eTxqz4mLkw5aHv+RuQzq6alErA=", "6YjW1XXr9g3cmdqk+rE9Cxxm8glRTovCXyyNmD0XM2A=", "2n3HOtkdKYpvd7rVvQa6bHnrwZ4OhrS9plk3MRDoPzQ=", "tiQ1ds9sesVo4Ql+TKHZ+DR6LuUTAZIi9VAxycVA6+g=", "gi0AxiPwfgSltV1PvkIrfCFpT3Tg8QtUH+zsd/yb35o=", "O4YeXDBStC4H+7DLvpOr2/r98Z1rWkiojqmHpzKwBWk=", "34D7PuY7JGw/tP4mYlfKem0G3YQHVv98Wk6Q6rXHFpE=", "AkLOgZMyQjQ1Sn+r28u+bAHkM28hf++5zW/hpWyeHDc=", "d2PgNADGRSALBa6O8wVw3HBmxCGzXKN2wh0flFap8Yg=", "iNHUjO59IboMOCstWbODlp1YYEmVT92QChJWA4B4jVM=", "FXwncoYUbqOOrzNPB3yyApMQDxQup50ymKYxvGMbrO0=", "llJ4XcGPKH04h81ae/iouz2FgGOLKJ00ng1SfbUW3gk=", "HHft4rHTTPo1mRMdYLcbYvIbJpjfE0WYHl/5fnUXsVw=", "8FAJJJyV0z/c38QXyUQZt6Z8Ax0kwE3QmwOeEsgnk2E=", "IaBVRpePWsHzWK9EHKB+zNzCjeTcWf9+kGzUa2GbCNQ=", "c4ZDZtYxfP1ijH61kx7c++JUAvkKvXeZOnCd8CkWFXM=", "xmEn8domWE/byYZDhXqU/GnGIxCe1j+BFnflyPpHRGs=", "98Zu2sXcEp0SwJ4c6ufifdMqfs03Hfd4hNo5ki31Whg=", "e0LdBPdkL0rUUVAmf4DchWE0mdCQAJ2s0smFcHWcyeY=", "vcwvDX1BALWpO32gXd/YeNmDRKzLikrk1eWmOzRvvbE=", "iCq+Nnm5goKzysn0TrMyruZ89OsMap3CoEMGklFFvHc=", "n9OHpc25HZ93St5SUltdJer74vYWPf1H0a8IUtBIFJA=", "+6T01efRmb8X38TRgBoL2G/5FPUs96JvFkNTqzEhjGA=", "zaHeBfdjVuPPIHwfYJ/Rah3tLN546y92VB4ipqQHghk=", "rbULWUUvD5opSVeq2MXynyRqi7gwEOm5lS4xStf3qrg=", "JSHofraUkQ+j8hTTaT90c3lLtJDoqncFlfUXsglPSYI=", "1bfZTkzpGU8R/qqtxuDBFB6CFXNLuL3DXVyWssS59gE=", "2eSmKKUpKwlI5/HsCGBlFrpQstcdtUK2wvGZDk9BfqU=", "ZjFVWSqGl8/f+rZ+ZzIn5dmcs6U/ESC0PqEv4LFtc8g=", "nzaSdePWFPonqss+2ZDHcDvawz2EYoUa2qK0YLeVw2A=", "t9nO9z/soHj+31Rp6mtq9cF467N7++lzYScA8S8pats=", "cTxvVwUaWv/etRVQpWzteEP7+bc6+WWEuGFfkBi8DMw=", "LZGDk92n2a5M4DCosxtNILKj1gstyTRibNbTqf95Mug=", "5Lb+d6gqLGRua2pnNUGIJJqvY6sTa55dUkkQoA3P2lg=", "3U3H8RI9ExtALwucBlLyCnMfxuwsL+NBPIs/Uuyf5H4=", "Orte7tsRcWY0rE7Xo5vDYWBSG6Z3FDgE9wcQpyKaYQ4=", "d4h0jR+5AynpkTANlSNQBjPccx2csO/GZZNbsGifmc4=", "lmzo5ImTCHx7LTUDAZcMDaIGeLq3h7VBMc1j3XCCNsM=", "q8la6rTBQ4X8TCb9/mvUpIK3kD/wqlyfUQeMs8MKUR0=", "JaWzOmM6sMcbOsMv0uZsYrwYl/eTq6iOnFk8fQUXA/c=", "XyFjzg2/83c9FfQlsf3j3Y9cBaIkOP07V15Ywej+mK8=", "u3jbnURJg57hK4P48IJQ1qWC9SUmBZ4o0aXmkhlPM6w=", "xwbrbjOOSGXENIOB1Yy8ZRsb7wTFVf9wDp40DNY9Qa0=", "sL6L6tKB6ovEccnnw95qOaToYoaJPx6otroDbi4g0Hc=", "8ve26SZp5nDHO13+5zG8cshIgryzrpPUB1HTw+9lS1o="], "CachedAssets": {"8ve26SZp5nDHO13+5zG8cshIgryzrpPUB1HTw+9lS1o=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\x3xne9n2zz-e3r9dq85uh.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Computed", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "TajneedApp#[.{fingerprint=e3r9dq85uh}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\TajneedApp.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "byhqz68cqx", "Integrity": "YziljzVs3JkEwclHWXkFlBxkqVMRUV0kkLrKVpNNeKc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\TajneedApp.bundle.scp.css", "FileLength": 540, "LastWriteTime": "2025-05-29T07:38:15.0891077+00:00"}, "sL6L6tKB6ovEccnnw95qOaToYoaJPx6otroDbi4g0Hc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\58sgrd8jrz-e3r9dq85uh.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Computed", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "TajneedApp#[.{fingerprint=e3r9dq85uh}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\obj\\Debug\\net9.0\\scopedcss\\bundle\\TajneedApp.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "byhqz68cqx", "Integrity": "YziljzVs3JkEwclHWXkFlBxkqVMRUV0kkLrKVpNNeKc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\obj\\Debug\\net9.0\\scopedcss\\bundle\\TajneedApp.styles.css", "FileLength": 540, "LastWriteTime": "2025-05-29T07:38:15.0875434+00:00"}, "u3jbnURJg57hK4P48IJQ1qWC9SUmBZ4o0aXmkhlPM6w=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\j8k8rx23td-yc14yep589.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/js/datatables.bootstrap5#[.{fingerprint=yc14yep589}]?.mjs.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.mjs", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8iep3heagz", "Integrity": "sDyxd6KmLh0PGSK/ojk2V28300rkcMcTZAM6gkkHv10=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.mjs", "FileLength": 853, "LastWriteTime": "2025-05-29T07:38:15.109334+00:00"}, "XyFjzg2/83c9FfQlsf3j3Y9cBaIkOP07V15Ywej+mK8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\rhymscq3ko-38jzjlrhc3.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/js/datatables.bootstrap5.min#[.{fingerprint=38jzjlrhc3}]?.mjs.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.min.mjs", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hh4wv7jzfw", "Integrity": "tcnrinJY3UtCDsoBflJED/eENZeg6ezxfddyl42Z+hI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.min.mjs", "FileLength": 587, "LastWriteTime": "2025-05-29T07:38:15.1076726+00:00"}, "JaWzOmM6sMcbOsMv0uZsYrwYl/eTq6iOnFk8fQUXA/c=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\4as9pmjux4-vp752pwqcj.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/js/datatables.bootstrap5.min#[.{fingerprint=vp752pwqcj}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3wpu9fsym2", "Integrity": "k2PE+9yJ344ibWctZCtgF2A2r1609Z62nZcJ9U86d4o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.min.js", "FileLength": 743, "LastWriteTime": "2025-05-29T07:38:15.1049328+00:00"}, "q8la6rTBQ4X8TCb9/mvUpIK3kD/wqlyfUQeMs8MKUR0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\mo3atkfexb-fjtml3ocfn.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/js/datatables.bootstrap5#[.{fingerprint=fjtml3ocfn}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jd149db236", "Integrity": "9wzh2z56neBBaeXMTvFdGZ2EjZl5AI1ifVLkhh+OZec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\js\\datatables.bootstrap5.js", "FileLength": 1178, "LastWriteTime": "2025-05-29T07:38:15.1035763+00:00"}, "lmzo5ImTCHx7LTUDAZcMDaIGeLq3h7VBMc1j3XCCNsM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\pv4pwfjqd5-mlv21k5csn.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-05-29T07:38:15.1015723+00:00"}, "d4h0jR+5AynpkTANlSNQBjPccx2csO/GZZNbsGifmc4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\e3mlaesewq-87fc7y1x7t.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-05-29T07:38:15.1015723+00:00"}, "Orte7tsRcWY0rE7Xo5vDYWBSG6Z3FDgE9wcQpyKaYQ4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\g8lpotsu2c-9vr69e8ynj.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=9vr69e8ynj}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mulfwnbtpa", "Integrity": "oheIXejBlri6EJgnT3rCEOV8cSc0XnYutP+KQ6pSvDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24363, "LastWriteTime": "2025-05-29T07:38:15.0971042+00:00"}, "3U3H8RI9ExtALwucBlLyCnMfxuwsL+NBPIs/Uuyf5H4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\vb1dc7l55k-jjsuc0puko.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=jjsuc0puko}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m8w5b0wq9e", "Integrity": "imJ8RM7oNfakmMXAji+mGEXDQYSPHKMWoVjRycEkdZ0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 69352, "LastWriteTime": "2025-05-29T07:38:15.0934875+00:00"}, "5Lb+d6gqLGRua2pnNUGIJJqvY6sTa55dUkkQoA3P2lg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\z5ri8hcg1i-ttgo8qnofa.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-05-29T07:38:15.1172352+00:00"}, "LZGDk92n2a5M4DCosxtNILKj1gstyTRibNbTqf95Mug=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\1qqyhyvf0n-m9qm4tazc0.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=m9qm4tazc0}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1s03qn3l9f", "Integrity": "T1Mxz6b5tcPGdGYeBLTL5A9PUSvbDbkClvy0G7J1r2k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30691, "LastWriteTime": "2025-05-29T07:38:15.1130197+00:00"}, "cTxvVwUaWv/etRVQpWzteEP7+bc6+WWEuGFfkBi8DMw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\dz62sxilx1-d6nrnj6jjx.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=d6nrnj6jjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2k5ha5qn83", "Integrity": "nuuauMZg92Lq1w9JnCHRfNGUNc9WeRQGkGwvzcHEyTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 85369, "LastWriteTime": "2025-05-29T07:38:15.1088099+00:00"}, "t9nO9z/soHj+31Rp6mtq9cF467N7++lzYScA8S8pats=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\19jwi9s4g1-x0q3zqp4vz.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-05-29T07:38:15.1015723+00:00"}, "nzaSdePWFPonqss+2ZDHcDvawz2EYoUa2qK0YLeVw2A=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\sjuinx3x68-gk0pw8i4co.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=gk0pw8i4co}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q07w49iitf", "Integrity": "itOFoWDZSqZIyZRKcH5nH/mbWKG8zHsAz7G0W9zjagg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8126, "LastWriteTime": "2025-05-29T07:38:15.0991078+00:00"}, "ZjFVWSqGl8/f+rZ+ZzIn5dmcs6U/ESC0PqEv4LFtc8g=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\5lcpkibyr6-worgj0nhwm.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=worgj0nhwm}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xj2ze9hdow", "Integrity": "rdkQBPBwlP4JzOj6r9lyBjOEX/yZn2usvoAwy9AWc1Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14178, "LastWriteTime": "2025-05-29T07:38:15.0978836+00:00"}, "2eSmKKUpKwlI5/HsCGBlFrpQstcdtUK2wvGZDk9BfqU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\j09i2ri0iu-mrlpezrjn3.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-05-29T07:38:15.0947327+00:00"}, "1bfZTkzpGU8R/qqtxuDBFB6CFXNLuL3DXVyWssS59gE=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\caqhplwkbu-83jwlth58m.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-05-29T07:38:15.0919735+00:00"}, "JSHofraUkQ+j8hTTaT90c3lLtJDoqncFlfUXsglPSYI=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\niscljinu6-356vix0kms.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-05-29T07:38:15.0875434+00:00"}, "rbULWUUvD5opSVeq2MXynyRqi7gwEOm5lS4xStf3qrg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\7pnd25z6x0-48y08845bh.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=48y08845bh}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5hgi50qbud", "Integrity": "F1eM1vgG9PaT7pF5Hubo7swbaVAWhXsjYwxoJvyk5+s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2206, "LastWriteTime": "2025-05-29T07:38:15.1114021+00:00"}, "zaHeBfdjVuPPIHwfYJ/Rah3tLN546y92VB4ipqQHghk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\z5bysyzta7-03hjc8e09r.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=03hjc8e09r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hj6zx16vtq", "Integrity": "I28FOsKinIPvKQJYJhPmeb5aWTZaShTculcMW3WSRdU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4679, "LastWriteTime": "2025-05-29T07:38:15.1099981+00:00"}, "+6T01efRmb8X38TRgBoL2G/5FPUs96JvFkNTqzEhjGA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\5py4bhyr65-3gk2gkf8pu.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/css/datatables.bootstrap5.min#[.{fingerprint=3gk2gkf8pu}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\css\\datatables.bootstrap5.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bn2wubp9h3", "Integrity": "Z4+inWfKzD1Dlb4iN/2ZIvv4tnR66/VbLx4WrGpPQ64=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\css\\datatables.bootstrap5.min.css", "FileLength": 2530, "LastWriteTime": "2025-05-29T07:38:15.1177533+00:00"}, "n9OHpc25HZ93St5SUltdJer74vYWPf1H0a8IUtBIFJA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\hdkhr08g7s-e8mwk64ned.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/css/datatables.bootstrap5#[.{fingerprint=e8mwk64ned}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\css\\datatables.bootstrap5.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "49dyq1i500", "Integrity": "mNWTD1S0Jw8vfxuCPG4kuNNOca5Fq0n3J+BlhI6tMqU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\css\\datatables.bootstrap5.css", "FileLength": 2636, "LastWriteTime": "2025-05-29T07:38:15.1167225+00:00"}, "iCq+Nnm5goKzysn0TrMyruZ89OsMap3CoEMGklFFvHc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\4up49nv7a9-0j3bgjxly4.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-05-29T07:38:15.1156218+00:00"}, "vcwvDX1BALWpO32gXd/YeNmDRKzLikrk1eWmOzRvvbE=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\7j1wwkgjuv-4d85u1mtcx.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=4d85u1mtcx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "47nw1ac886", "Integrity": "cAYCo2Q8Ith/zqZVNFuI/cRoMoGlmld9t1DuyUGILPk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16642, "LastWriteTime": "2025-05-29T07:38:15.1110598+00:00"}, "e0LdBPdkL0rUUVAmf4DchWE0mdCQAJ2s0smFcHWcyeY=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\yp8pnxzlnp-h1s4sie4z3.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-05-29T07:38:15.1068603+00:00"}, "98Zu2sXcEp0SwJ4c6ufifdMqfs03Hfd4hNo5ki31Whg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\m5d864i7bj-u9q1upor1n.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=u9q1upor1n}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f711a7kh87", "Integrity": "pf2AOjAOccWBEl9ORfBaaHo0t3lQUfisiqfIeMW9tqI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29774, "LastWriteTime": "2025-05-29T07:38:15.0983867+00:00"}, "xmEn8domWE/byYZDhXqU/GnGIxCe1j+BFnflyPpHRGs=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ahu6m88gcn-y7v9cxd14o.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-05-29T07:38:15.0929835+00:00"}, "c4ZDZtYxfP1ijH61kx7c++JUAvkKvXeZOnCd8CkWFXM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\grxx96oju3-8vyfqsqgz1.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=8vyfqsqgz1}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0v96h8eab3", "Integrity": "bKh1MfaBAldvWxNRIEd7ZCFzOhTySMaAYostY/K7XbA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18643, "LastWriteTime": "2025-05-29T07:38:15.1182713+00:00"}, "IaBVRpePWsHzWK9EHKB+zNzCjeTcWf9+kGzUa2GbCNQ=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\0qu2fmqnfk-kbrnm935zg.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-05-29T07:38:15.1156218+00:00"}, "8FAJJJyV0z/c38QXyUQZt6Z8Ax0kwE3QmwOeEsgnk2E=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\j1rtvjdsvi-ltid2c489k.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=ltid2c489k}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jgdip1sczm", "Integrity": "ozEUTWBbc3wKZYb3ecLdk+9yMj3kbTTBJx6aTKh2FDs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 29043, "LastWriteTime": "2025-05-29T07:38:15.128019+00:00"}, "HHft4rHTTPo1mRMdYLcbYvIbJpjfE0WYHl/5fnUXsVw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ifcnnv47is-iovd86k7lj.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-05-29T07:38:15.1258209+00:00"}, "llJ4XcGPKH04h81ae/iouz2FgGOLKJ00ng1SfbUW3gk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ivo05jppsk-hd3gran6i8.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=hd3gran6i8}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yc8010sx1o", "Integrity": "rjTJ9Ut8h6OLoNYsNgr7e/TIritk83K5bucjnu0BPlM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23992, "LastWriteTime": "2025-05-29T07:38:15.1198221+00:00"}, "FXwncoYUbqOOrzNPB3yyApMQDxQup50ymKYxvGMbrO0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\l1ksyiojky-6pdc2jztkx.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-05-29T07:38:15.1182713+00:00"}, "iNHUjO59IboMOCstWbODlp1YYEmVT92QChJWA4B4jVM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\xsbbsidfg6-4094rpi4f9.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=4094rpi4f9}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u3mr700haq", "Integrity": "qk/LGv3UCKBZhVKSwN0Lvyprj8TiWmp+iiWhP0Dwdt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44640, "LastWriteTime": "2025-05-29T07:38:15.1081769+00:00"}, "d2PgNADGRSALBa6O8wVw3HBmxCGzXKN2wh0flFap8Yg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\vw7dhz31n8-ft3s53vfgj.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-05-29T07:38:15.1015723+00:00"}, "AkLOgZMyQjQ1Sn+r28u+bAHkM28hf++5zW/hpWyeHDc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\bx5gql4yz4-c63t5i9ira.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=c63t5i9ira}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n2nwmiwapx", "Integrity": "zxq4fMIq5J99E6EK1H7qVe10DUZ2GcXauY+j6URB7DI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30991, "LastWriteTime": "2025-05-29T07:38:15.0929835+00:00"}, "34D7PuY7JGw/tP4mYlfKem0G3YQHVv98Wk6Q6rXHFpE=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\1w6kpgm46c-hrwsygsryq.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-05-29T07:38:15.1224622+00:00"}, "O4YeXDBStC4H+7DLvpOr2/r98Z1rWkiojqmHpzKwBWk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\jhlg3z209o-ynyaa8k90p.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=ynyaa8k90p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0uw774o5yt", "Integrity": "CKd1DZe0tmjYGXJCdpcyExOT7GvFEZKGtk3+Ij7ZTSc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33374, "LastWriteTime": "2025-05-29T07:38:15.1130197+00:00"}, "gi0AxiPwfgSltV1PvkIrfCFpT3Tg8QtUH+zsd/yb35o=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\vhzpi13y9u-v0zj4ognzu.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-05-29T07:38:15.1264298+00:00"}, "tiQ1ds9sesVo4Ql+TKHZ+DR6LuUTAZIi9VAxycVA6+g=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\7gi0sn68zr-43atpzeawx.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=43atpzeawx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cmcmkw9yu7", "Integrity": "ZA1L2PX1u6VKIuxhJUgeKELAZwIreHb7fDdqlTEZyKs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30968, "LastWriteTime": "2025-05-29T07:38:15.1187822+00:00"}, "2n3HOtkdKYpvd7rVvQa6bHnrwZ4OhrS9plk3MRDoPzQ=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\s66ds0h340-pj5nd1wqec.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-05-29T07:38:15.1156218+00:00"}, "6YjW1XXr9g3cmdqk+rE9Cxxm8glRTovCXyyNmD0XM2A=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\xukadz714f-zub09dkrxp.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=zub09dkrxp}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nqh38a62u8", "Integrity": "j8xOcokVqVMlkhtMkAD+dCGjp8eCK+RXUPREaOqe2TU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33518, "LastWriteTime": "2025-05-29T07:38:15.1049328+00:00"}, "uinZvGV4Hwree+sH6eTxqz4mLkw5aHv+RuQzq6alErA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\sucryzqgkr-nvvlpmu67g.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-05-29T07:38:15.1000772+00:00"}, "4RwrHFICq8IKQnGtCBeqCv/cQEY43BH8W5d8sPLzp7Q=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\695e9z8lx3-keugtjm085.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=keugtjm085}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q8bg8bu1pt", "Integrity": "Fi8DMQpQ0pHVk8uDPzXmz7v1U9ElGm/J/lTUl54WMHw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11050, "LastWriteTime": "2025-05-29T07:38:15.0964168+00:00"}, "cV2C23PvXDzvOcIdZyudET/9S+XhVbOpQaGvz5FC7ls=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\9ahs3oxegm-j5mq2jizvt.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-05-29T07:38:15.0941261+00:00"}, "PcvJUISzN/RgY+Noz4qljeV5WC+PxivQMDIrcENqNj4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\c35cgxkat3-d4r6k3f320.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=d4r6k3f320}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42ih0tfzwc", "Integrity": "rLnVjI3ToFgRadgC+qnQqkjBbeGzyUk/W1M+MRurfiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 12033, "LastWriteTime": "2025-05-29T07:38:15.1119056+00:00"}, "IA4wUZig+a980dEj9z50MtL28v3AqbdGDyz0+xJS/ac=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\sau7oekj11-c2oey78nd0.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-05-29T07:38:15.109334+00:00"}, "Na3eGxe2vNCfeTERy9tWMRTqI5eU22BLwQndSxjtVfA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\224ghrl0aq-wl58j5mj3v.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=wl58j5mj3v}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nv0k9gv8u2", "Integrity": "cxoqFtEDkm4KCYDrmWgnE7zkiXwSKUlq/rctM76ouDg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11067, "LastWriteTime": "2025-05-29T07:38:15.1081769+00:00"}, "AuLHnvOfddrN7RcHT2EbaHcfiSg19VXFo3RCBDWOPuI=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\6dc0l2jxb7-r4e9w2rdcm.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-05-29T07:38:15.1049328+00:00"}, "6yxGfggJZhdxzacEMZH7QfDGVQ3HBDnTZsZrUR1muO8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\uz2lyigxms-gye83jo8yx.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=gye83jo8yx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k64ed53csw", "Integrity": "VGKPtySribew+qZrTcy+fpnTi7Q3gxSv+HWQQ/4rQos=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 12081, "LastWriteTime": "2025-05-29T07:38:15.1000772+00:00"}, "4FPhWcf+HBSe77kWhCIbPU4SrvgQYawk55crblER4As=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\60h6ccmi8t-jd9uben2k1.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-05-29T07:38:15.0983867+00:00"}, "+V6BDRmweiUk+e1pWI63DoIuzVje2XKPS2XSJqXrXDw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\zfzxseweal-q9ht133ko3.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=q9ht133ko3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5fijz6kmv0", "Integrity": "AX9UjSDaqolhmzTa/KUDWMjLF2XlYT6I6JZuUGrm1nw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3250, "LastWriteTime": "2025-05-29T07:38:15.0947327+00:00"}, "bAWkyBAHQir18iI9of2ZvKImlOQaWdnMdf+a67NAqDo=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\sfyrk4wm38-ee0r1s7dh0.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-05-29T07:38:15.0929835+00:00"}, "W6mYqW6t7O9QEtJXLy4eE9hcBYii7t3AsCPge8eN0WU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\cqwfqr3kf0-rxsg74s51o.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rxsg74s51o}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xmcjt9e9cy", "Integrity": "ueerZxLdSKIEsWJ13wzBfTcRfd3uGSfOQwhUfM1jRec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3397, "LastWriteTime": "2025-05-29T07:38:15.0891077+00:00"}, "vkEeM9h9dYZSTEGd1LMu2g6qulwyE00+Og52WMYKh6Q=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\zde3zg7pik-fsbi9cje9m.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-05-29T07:38:15.1076726+00:00"}, "jdfKRlDv5I+CKL94fJQ+RRC9hJNl5mHAP5447kUVVfo=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\gixzetcklg-tmc1g35s3z.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=tmc1g35s3z}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7jljgo6o9r", "Integrity": "w8PM3Ms+TEJVQ/PGIsRL/kn0h83Pm5ek8Sm7/zkgCYc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3217, "LastWriteTime": "2025-05-29T07:38:15.1035763+00:00"}, "5ebkHCXi6BdYht9h2z49wcXORUJaoyehsqJ/sDRaSNA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\gthtcsx6jh-fvhpjtyr6v.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-05-29T07:38:15.1081769+00:00"}, "6NwSLArDwfW3vn0YEqJsr7gIx9O0Ed6hGV50R6nEpvU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\l7hecbek5s-qesaa3a1fm.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=qesaa3a1fm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dtwnk525ov", "Integrity": "H9IPXBzUtJGG7PJdjNTz1WD7YcFYCi+22bHkHPR+CdY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3407, "LastWriteTime": "2025-05-29T07:38:15.1035763+00:00"}, "xZ0vEpUiypCHXjtlRQUkVnJnqlxftn1LTwjLsU8KLKk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\qc852yxj2z-cosvhxvwiu.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-05-29T07:38:15.1015723+00:00"}, "g3r85+tiFuakGdNpW5ug7E/Dg4g6Tg1DDAvCo8RTHe4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\g61qy9qscw-22vffe00uq.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=22vffe00uq}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mxqr4d2em4", "Integrity": "Gjkg0hmhA9Aga0f2ZS8ZqJ/GES4kxIvtWTfabr1bc2Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5976, "LastWriteTime": "2025-05-29T07:38:15.0991078+00:00"}, "PgOARTXdqvf5ocS5KY/omtOY9zN7u8HqWHKEIUT60wM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-05-29T07:38:15.0897303+00:00"}, "7FPF+LpE1QN+jjVy6GLge8WUtrSQL4EMe7vqJVnOSp4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ok5a1ddg8e-e76ncinqlp.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/js/datatable#[.{fingerprint=e76ncinqlp}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72ex96jm7c", "Integrity": "kQkzmckB+YbyfT491HVgWjD50eEw1QbaAK4awOKnrfc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.js", "FileLength": 11002, "LastWriteTime": "2025-05-29T07:38:15.0891077+00:00"}, "YeRfAKZLtXVdL+3YaO80WC+Q6iBi/vkW3RrskrF5F2s=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-05-29T07:38:15.1246463+00:00"}, "xwbrbjOOSGXENIOB1Yy8ZRsb7wTFVf9wDp40DNY9Qa0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ueryi6faph-gm2o380hld.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "localization/ar#[.{fingerprint=gm2o380hld}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\localization\\ar.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pxdomnap16", "Integrity": "czGSHctXL+Wmbu5aW1BNOHtjwbfpx3GSeaRwS2VgQ4c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\localization\\ar.json", "FileLength": 1971, "LastWriteTime": "2025-05-29T07:38:15.1105013+00:00"}, "vu2qEBgFTMwMhzAYhKI65kT7OiaLRdMQCR6Dzq7RJzI=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-05-29T07:38:15.1119056+00:00"}, "njmFt58KBI6WjiMHJTniHc69QrRckgZ5jzfbg+kOH+o=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\1cx5ci8a2y-298x6fratd.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/css/datatable-bootstrap.min#[.{fingerprint=298x6fratd}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable-bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glkolfxhns", "Integrity": "RJ9FhH7h9xR6LgrrGEdxY3imB7N1C3Im5nfONVUl550=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable-bootstrap.min.css", "FileLength": 166, "LastWriteTime": "2025-05-29T07:38:15.0941261+00:00"}, "hrV0K0wuRt/hXvWuZx6cNOuDKz0vtmzG3n62GK9spoQ=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-05-29T07:38:15.0983867+00:00"}, "cyngyNoCNk+6APjoX7YSqDGQFvBtiUKNetgXt3RdhHw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\21q1iv0nyk-0dyv18pzof.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "css/site#[.{fingerprint=0dyv18pzof}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ctbvroxfu7", "Integrity": "cIcFRtvlXxaej2eU/DvCTvqsuyOL575jT0wJxzqDOf8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\css\\site.css", "FileLength": 1468, "LastWriteTime": "2025-05-29T07:38:15.0919735+00:00"}, "52u1Q3R3xnGuLBLOY+HHLax4fuOaxACu++DYDGGac4c=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-05-29T07:38:15.1099981+00:00"}, "9GT19f6PtxgCEa1LIYuLa7cHr08TQXQwX1oubn9rN0A=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\um3zez7ymr-dwmw1nforb.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "css/dashboard#[.{fingerprint=dwmw1nforb}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\css\\dashboard.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pgmlqv6tiq", "Integrity": "+TqlXBEnAMgS9iL6/MbQnYKKAwlQm1a3wb6F5IoZ9Ng=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\css\\dashboard.css", "FileLength": 621, "LastWriteTime": "2025-05-29T07:38:15.0880654+00:00"}, "gjedseVVrWwgN8j5qkgm5mgD+zOV2GGEuxsYWSvyEFw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-05-29T07:38:15.1177533+00:00"}, "NxBJTQkNs1SD7uc40EeiVHFU03JJCgW3tDGcmWKoo38=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-05-29T07:38:15.109334+00:00"}, "eY+skmEETWtxSm3wlEj5Rkc1QKcb84k31gZjud/nJHU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-05-29T07:38:15.1236098+00:00"}, "+yg+iF6GI2njBZXAzcxkmfUz/m6qZpHv1Q+NK9NPnE4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-05-29T07:38:15.1167225+00:00"}, "oD/Jkm6bftYPrHxxAYyreLKlF/f755GkYQhjj/A7SmQ=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-05-29T07:38:15.128019+00:00"}, "ULgop/Djmjnk5c6l1AfXh7z18e+2vMa+3cLy63OmzAQ=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-05-29T07:38:15.1081769+00:00"}, "WUPaSsH5xBeawlFOzNWDzJOSm2kY/453T6XRqjMCoDM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-05-29T07:38:15.095888+00:00"}, "4Sf6TGrOSnXFrPYldxMFvS8NStqQla2D+4mH/yvSRjs=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-05-29T07:38:15.0983867+00:00"}, "twfVFPYWSKRToqnDQ7gep5bTKANsZzs2iZIubZrRxK0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-05-29T07:38:15.1000772+00:00"}, "0UoYawkV2PFQQ5p4X+bc7jsUlzdsBA4RDw6gdYN8KMk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-05-29T07:38:15.1105013+00:00"}, "JrKtF9F/JtlZFxLsOY8qKaE48EgOPvZYRzNNA1uoySg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-05-29T07:38:15.1035763+00:00"}, "lWs7kAVBG+TjP4MMv35DQ7K7qXHJ4sJlsXmIfHxFGrk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-05-29T07:38:15.1076726+00:00"}, "06Su6gSdGDrSvGArTFTFbm58dIiPDvPfGrl0dLt8i7M=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-05-29T07:38:15.1088099+00:00"}, "pKJRl95iMVZDPxNv4T7oEOJFFtjxGDNTocbsl9fPxBI=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-05-29T07:38:15.1015723+00:00"}, "SwZryww/2IMUUAeRvji+TNz0URg5ZSIrnTzfl5QofvY=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-05-29T07:38:15.0891077+00:00"}, "M7hVsbHWh05EfNyshwD+K1LeqxPfu83XrjX4hbmFjLw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-05-29T07:38:15.0978836+00:00"}, "0BWVCAzNEaQdYRlG/exz60CaN8IJsaUc/2UmaZU2Qxc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-05-29T07:38:15.1015723+00:00"}, "ZrnQEDQGQ35efGH9yA1HMA3DNWKfFnnoIh9SyMOnUQc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-05-29T07:38:15.0897303+00:00"}, "vB6O7p4siGaTvivXbEjDEqo/R0WYHIyAbAOgYw8aeTQ=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-05-29T07:38:15.1145878+00:00"}, "0/YuKijMBsnKGvk2ErPC1+1Rag09V8OQxz9nhLHND9E=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-05-29T07:38:15.1264298+00:00"}, "GwI1LHkS+k2L3zCoYBhWDuM+2nF+Grz6V60tvYxIaIg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-05-29T07:38:15.1203558+00:00"}, "eFEy1mSCz8swvXc65Xl3L8RujoB4wrnGfzVpE3mJU60=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-05-29T07:38:15.119304+00:00"}, "rvx0scU97oxQWhu/aynFOsXNzzpesuJP+5oX+vAwDjc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\5wyh8fqgul-fanqqar6vz.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "css/custom#[.{fingerprint=fanqqar6vz}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\css\\custom.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hmro0q6hwe", "Integrity": "JxbFu32o/A6xFh/OmT41pSL5aixyobqSrzISnPfHHW8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\css\\custom.css", "FileLength": 3366, "LastWriteTime": "2025-05-29T07:38:15.1105013+00:00"}, "cBxz5xXiJOXXM7y+M5ZCjn7PYObGYN4yiNjOpLKi+e8=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-05-29T07:38:15.1099981+00:00"}, "SAwBC2SbtpIAwMPCllm573ghV5xC9T/iawN3Gfg66dw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\hw370xkdza-dvvvkbu0fz.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "css/sidebar#[.{fingerprint=dvvvkbu0fz}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\css\\sidebar.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0f1yjnmmaf", "Integrity": "rMouX0jEywwQ08BIxHNj6g0204jcTTXHbec+8+QvRGY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\css\\sidebar.css", "FileLength": 987, "LastWriteTime": "2025-05-29T07:38:15.090289+00:00"}, "JMLbg6hz2zFYHZONHXokfOjSx4MkPYyVNTzf1grY2fQ=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-05-29T07:38:15.1015723+00:00"}, "hahJllGQp7jaaRKZplhTX92dO56eBXaZVh5iWWRol6A=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\0rzj6l2fpt-t27oiczu7f.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/css/datatable-bootstrap#[.{fingerprint=t27oiczu7f}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable-bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pletbzcagi", "Integrity": "03kh08q3wnXtgu/dTeKxHcQfRk5NSjgZ319uVtvi2DQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable-bootstrap.css", "FileLength": 190, "LastWriteTime": "2025-05-29T07:38:15.0929835+00:00"}, "A4kiEaJC8Fd0Ia4QjUihnTVYPyA/ZrVnYvYex3mXnhU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-05-29T07:38:15.1162045+00:00"}, "f8rwNAFfe27IJbHX+FBDM+kmixEelGpiuy/870E9qWc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\wac810xtcm-c0yflasb9m.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/css/datatable#[.{fingerprint=c0yflasb9m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1auxemu7qt", "Integrity": "iZ3evWqMT35yQOTrfHLLoD/sfXY2hdNBpONkTQ+udOE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable.css", "FileLength": 377, "LastWriteTime": "2025-05-29T07:38:15.095888+00:00"}, "WLc0XLZebDkav8O7367sV9zBzrJDRS85UfooePwTIRU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-05-29T07:38:15.1274907+00:00"}, "HQ47DLCWOKLY8/XhQhYBAfZpw9MjM8TuWDTWBdxZN2c=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ccb3zsii90-gmktcc4tyv.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/css/datatable.min#[.{fingerprint=gmktcc4tyv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rizmv1579y", "Integrity": "U5AFoUXYWOs6Upnp74FTJfOc/Mest39PWqN5Sy7rQo4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\css\\datatable.min.css", "FileLength": 286, "LastWriteTime": "2025-05-29T07:38:15.0964168+00:00"}, "VurvlpAmn+Je5LfHdhcmbeL1nVE73jgK9eOvBJBusPg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-05-29T07:38:15.1156218+00:00"}, "j/y8vDxl2/3X2Xz/0KgYsSIzjXboY45knME2J3mVctU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\jk0mq5gmrl-r6y55mx65a.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/js/datatable.jquery.min#[.{fingerprint=r6y55mx65a}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u7z45wkmzb", "Integrity": "SvCNF3LL4mgVJtreJAu13Z58W1Ul3jL5YC5Qbh+yfnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.jquery.min.js", "FileLength": 625, "LastWriteTime": "2025-05-29T07:38:15.0991078+00:00"}, "cLcIMclCrtzLPYkhmkduDASOUulpvGoCZjCN0MFhDiA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-05-29T07:38:15.1105013+00:00"}, "JuScLr0KUG2kSbNgkrAk4sEgbqiiC9RIXcxvh9VnYF4=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\czdec1f1i9-reqyzuf3o7.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/js/datatable.min#[.{fingerprint=reqyzuf3o7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2j7vh1caua", "Integrity": "IjPifJJIaIyYR1EDWQM+sE1EtIwmWTA4A3vTAftn7PU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.min.js", "FileLength": 6015, "LastWriteTime": "2025-05-29T07:38:15.090289+00:00"}, "SbylYTytt8siB04WU8vXnu5m9tJz33GEgp+cFyr3SSc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-05-29T07:38:15.0929835+00:00"}, "NIpQK5GwhQKDiLYCMt/98NXPumLhVBuQbtzaHClh0Ks=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\0wkx376cxg-61n19gt1b8.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-05-29T07:38:15.0919735+00:00"}, "NCUChOuSDm16yvf2gp/rDafRBvgiPuoy0VLsKXvOkq0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-05-29T07:38:15.1015723+00:00"}, "5AS2haTeJL9mJ+YSUFwN4deNLwefUikrEw3t1PC6hpc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\2hjkq69mky-34bjharz5s.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "js/dashboard-charts#[.{fingerprint=34bjharz5s}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\js\\dashboard-charts.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h5rk824j3r", "Integrity": "dRbbiCEnhkcIxyEtKypO+f5B8pikdq+fMMqzvvvHkWY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\js\\dashboard-charts.js", "FileLength": 1372, "LastWriteTime": "2025-05-29T07:38:15.0941261+00:00"}, "47gSoLXagNZjBcot69fu7lV8VJ/KUjZKY7fJRp0Ej2g=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\rxnhbv93y9-wwhvu3l9iz.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "js/login#[.{fingerprint=wwhvu3l9iz}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\js\\login.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5a19u0hby1", "Integrity": "vRxKCHBS/W9XVwSRoQ/jno0jCT46y1hDB3oOPqeMvgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\js\\login.js", "FileLength": 1705, "LastWriteTime": "2025-05-29T07:38:15.0964168+00:00"}, "stVp+bYgsYKoakW8NMMRvM+YLtcZ4AUg4v/U/9CkXoo=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-05-29T07:38:15.1000772+00:00"}, "cwcN+es/Fu6yn4oVATAhlHXwU+VQf9I414ceCGiiqak=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-05-29T07:38:15.0929835+00:00"}, "q3t0th6aUesY3XrCrVAL1nKjf6ddKdKevaxej6NvVHk=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-05-29T07:38:15.0897303+00:00"}, "vDCgyf5SPAUNGM6BloDLQHvV0yU6NcWDn+OIzeO/kMs=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\yng81s5emu-78x9uzmf1f.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "js/site#[.{fingerprint=78x9uzmf1f}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mi026k8bll", "Integrity": "4rrRkWoK7cek8Jn8U7K6vFWy9C5pQcPyEbkfL/QBlYA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\js\\site.js", "FileLength": 393, "LastWriteTime": "2025-05-29T07:38:15.0983867+00:00"}, "ZTqZ8E+MLAmYbjQ7joDsdJhhuaLR6ldeswN1tRgi1Vc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\9lzoulic0o-t1cqhe9u97.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=t1cqhe9u97}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7trewrvtze", "Integrity": "td/WnErsJHKP3LT8Bz6znCdABuCWnP513FYSQpHRK9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6754, "LastWriteTime": "2025-05-29T07:38:15.1000772+00:00"}, "eqobdvid3iKTYLdiLMhQ1HMdNbNeHvXll65UoetZKt0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-05-29T07:38:15.1041834+00:00"}, "mI1qu8+zRFbwmWWlyCOdIs5yZ/VoNtkGM3Hwqd+ddaE=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\4uo46uk8cw-c2jlpeoesf.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-05-29T07:38:15.1041834+00:00"}, "w6jQWkFcgJXHfDt0OSlPekGX8RhPbVt4y2+oUvajJho=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\snnbr3djt1-sejl45xvog.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=sejl45xvog}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mhey2fgxm6", "Integrity": "2psAeYsQwIU+4hjhaa7aiuEI8eM0VqXG4mLMI2LB5kg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5973, "LastWriteTime": "2025-05-29T07:38:15.1049328+00:00"}, "CfBukD50bstg+4g9U1v5s4pVz0HzKmwMEVnskogQXjg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-05-29T07:38:15.0971042+00:00"}, "bnlx8XEt9V6JNHiwLs6WCvDjF1CJYXAd4Cji/h7Y2lE=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\01flxr017q-aexeepp0ev.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-05-29T07:38:15.0891077+00:00"}, "GQW+b2EBWppcCNGU4vXERJNPpEQvP17D02mimXxJu7E=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\g4pes73y9h-xvp3kq03qx.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=xvp3kq03qx}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1xcrflemqm", "Integrity": "Jb3hghPX8HIvLINQ2LRDLH33mFfqyzbk+nTsbiHtKr4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6755, "LastWriteTime": "2025-05-29T07:38:15.0926783+00:00"}, "ep2qditTFDN7q+8ws33qSgLmocMWJq2jgRYbCloOGoA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-05-29T07:38:15.0885822+00:00"}, "yRpdHjChkc+H33BTD4/iJ9Ah0e5PojDavurwIi56qvM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-05-29T07:38:15.0947327+00:00"}, "mCZLZntD+5BBNkwM/mG6AE6+t6KI9tbVKtyM4GmENMU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-05-29T07:38:15.0897303+00:00"}, "CdvuOY15cc6LcQ0uuT5LgfdHI7QW6A6cuAfszfoDTbs=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-05-29T07:38:15.0875434+00:00"}, "MOTVvzzuYtkqUHPs4dEnB2gq0iUXELsjMyn7ij+CLyQ=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\dj0tcf9tdp-ausgxo2sd3.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-05-29T07:38:15.0971042+00:00"}, "A/twmKlk9CofUGja9/y5oLxhsjBMUU3bSvzzHcPgVEs=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-05-29T07:38:15.1114021+00:00"}, "sqAXIpFzO0p3AZhnCq3cXedKzGAac4CpU1Xc721AIR0=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-05-29T07:38:15.1049328+00:00"}, "bL/ewdInZCj94pwaH1ZOD4Ea9W1ubYBj6TSZ22AtR0k=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-05-29T07:38:15.1049328+00:00"}, "mDSwzn4Mq9O0XSeZxa7x46D6j8sIyR5NwTTd6OUDMYs=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-05-29T07:38:15.1105013+00:00"}, "GYG98W0fXFTtZkUqdq5tvczrlvvcnO0avCO7P5Mfu30=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-05-29T07:38:15.1049328+00:00"}, "hgRdAdYZCGZ5aIPFNNL02PtGB+3T5+7UMyYGGXNg5HE=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-05-29T07:38:15.1015723+00:00"}, "mknBfoluqwN1c1hrDDggUyFK9QoPOffSUGbn3GixHKU=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-05-29T07:38:15.0983867+00:00"}, "Y5n3GM5DN3qaB0NvpOo11+oec5ZjIYMc03gpPb91260=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-05-29T07:38:15.0991078+00:00"}, "kkiBwAFp7c921+DgtXwQQ+0E8cf4NQpXPh2dO419EQo=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-05-29T07:38:15.0971042+00:00"}, "/CrRW+yA8Yh9Va6r5qSO6frhspC12STXV94/ocKPeao=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-05-29T07:38:15.1015723+00:00"}, "3oh14yhJh5cM40XqVDbrCjzh0r8d2/NqjTK9uL5edMA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-05-29T07:38:15.0991078+00:00"}, "YqDMAKhd+rkQO91XpNbeM04jcxcRClR4LJCJryPLPQg=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-05-29T07:38:15.0929835+00:00"}, "7RaGvse+Nsf7AThgj25/BEHGmSigCSjCzOL4m22S5nc=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-05-29T07:38:15.0912362+00:00"}, "UbHMvTh12Af6hn62BiQrM1SRLwzaKFLGhK+2CUs/W6w=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-05-29T07:38:15.1015723+00:00"}, "kt+qIJZ/1f2IWL78300Vqtfd0ws3fUECmpFv1RnYICM=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-05-29T07:38:15.0991078+00:00"}, "wK7crylrKijUmCmnmMjlbZMoW7CuLhYhunUXhR8A7hA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-05-29T07:38:15.0964168+00:00"}, "MBpry5QQ204jFFun3qgcL7KP4G4uO8YNqooRWdRkPqA=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-05-29T07:38:15.0941261+00:00"}, "jaJHSpWfVNBI13hCjieGyqiz3ClVqqAGiin4HjmdOOw=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-05-29T07:38:15.0929835+00:00"}, "DIXtw+LCWsT1RjtrsxQSwcXLgfK7QNuEen4b4QWNweE=": {"Identity": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\mohobw47eg-zzb78u50n5.gz", "SourceId": "TajneedA<PERSON>", "SourceType": "Discovered", "ContentRoot": "D:\\TajneedApp\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TajneedApp", "RelativePath": "datatable/js/datatable.jquery#[.{fingerprint=zzb78u50n5}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ltu4apqqj8", "Integrity": "9xx1ifaCHtfStd6WqcTVHQiNb4D18mEkTr5lj7CnnAw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\TajneedApp\\wwwroot\\datatable\\js\\datatable.jquery.js", "FileLength": 857, "LastWriteTime": "2025-05-29T07:38:15.0978836+00:00"}}, "CachedCopyCandidates": {}}